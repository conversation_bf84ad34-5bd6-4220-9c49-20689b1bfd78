import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:maria_filo_app_flutter/config/design_tokens/widget/clock_layout.dart';
import 'package:maria_filo_app_flutter/config/design_tokens/widget/spot_clock_layout.dart';
import 'package:soma_ui/soma_ui.dart';
import "package:soma_core/soma_core.dart";

import 'icon_tokens.dart';
import 'product_details.dart';
import 'sizes_table.dart';
import 'terms_and_messages_tokens.dart';
import 'text_transform.dart';

DesignTokens designTokensBuilder() {
  final typography = DTTypography(
    colors: _colors.typography,
    fontFamilyPrimary: _fontFamilyPrimary,
    fontSizes: _fontSizes,
    lineHeight: _lineHeight,
    defaultFontWeight: FontWeight.normal,
    headlineLg: const TextStyle(
      fontWeight: FontWeight.bold,
    ),
    headlineMd: const TextStyle(
      fontWeight: FontWeight.bold,
    ),
    headlineSm: TextStyle(
      fontSize: _fontSizes.md,
    ),
  );

  final mariaFiloDesignTokens = DesignTokens(
    colors: _colors,
    spacingStack: _spacingStack,
    typography: typography,
    borderRadius: _borderRadius,
    borderWidth: const DTBorderWidth(
      widthSmall: 0.5,
      widthMedium: 1,
      widthLarge: 2,
    ),
    icons: _icons,
    termsAndMessages: mariaFiloTermsAndMessagesTokens,
    sizes: const DTSizes(
      addTobagProductHeightImageSize: 230.0,
      favoriteButtonSpotProductIconSize: 24.0,
      favoriteButtonPDPIconSize: 32.0,
    ),
    textTransform: MariaFiloTextTransform(),
    pdpTheme: _pdpTheme,
    feedbackTheme: FeedbackTheme(
      regular: RegularFeedbackTheme(colors: _colors.feedback),
      outline: OutlineFeedbackTheme(colors: _colors.feedback),
    ),
    spotProductClockTheme: SpotProductClockTheme(
      simpleClockBuilder: ({
        String? coupon,
        String? couponText,
        Duration? remainingTime,
        Color? backgroundColor,
        Color? foregroundColor,
        String? callToAction,
      }) {
        return MariaFiloSpotClockLayout(
          coupon: coupon,
          couponText: couponText,
          remainingTime: remainingTime,
          backgroundColor: backgroundColor,
          foregroundColor: foregroundColor,
          callToAction: callToAction,
        );
      },
      textSecondaryWidgetBuilder:
          (String? subtitle, String? coupon, TextStyle? style) {
        if (coupon?.isEmpty == true) return null;
        return RichText(
          text: TextSpan(
            style: style,
            children: [
              const TextSpan(text: 'CUPOM: '),
              TextSpan(
                text: coupon,
                style: style?.copyWith(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        );
      },
      titleIsCenter: true,
      ctaIsCenter: true,
      ctaIsBottom: true,
      ctaIsBelowHeader: false,
      titleClickable: true,
      showIndexIndicator: false,
      containerHeight: 350,
      textPrimaryStyle: typography.typeStyles.headlineMd.copyWith(
        fontSize: 14,
      ),
      textSecondaryStyle: typography.typeStyles.bodyLg?.copyWith(
        fontSize: 12,
      ),
    ),
    bottomSheetSizeTableTheme: BottomSheetSizeTableTheme(
      hasScrollableHeader: true,
      headerPadding: EdgeInsets.only(bottom: _spacingStack.lg),
      bottomSheetHeightFactor: 0.7,
    ),
    sizesTableTheme: SizesTableTheme(
      sizesTable: const MariaFiloSizesTable(),
      tableHeaderHeight: _spacingStack.lg,
    ),
    productsGridTheme: ProductsGridTheme(
      productsQuantityPlacement: ProductsQuantityPlacement.navbarSubtitle,
      navBarActions: const [
        SmSearchButton(),
      ],
      navBarTheme: ProductsGridNavBarTheme(
        backgroundColor: _colors.neutral.pure1,
      ),
    ),
    catalogFiltersTheme: CatalogFiltersTheme(icons: _icons),
    emptyBagTheme: EmptyBagTheme(
      imageSize: 60,
      buttonTextColor: _colors.brand.pure1,
      buttonBorderColor: _colors.brand.pure1,
      imageColor: _colors.neutral.medium2,
      subtitleAlign: TextAlign.center,
    ),
    spotProductTheme: _spotProductTheme,
    productCardTheme: ProductCardTheme(
      lowStockTagColor: _colors.neutral.pure2,
    ),
    bagTheme: const BagTheme(
      badgeSize: BadgeSize.medium,
      positionRight: 4.0,
      positionBottom: 2.0,
      productsSpacing: 8.0,
      inputCouponSpacing: 8.0,
    ),
    inputTheme: const InputTheme(radius: 0),
    snackBarTheme: SnackBarTheme(
      borderRadius: _borderRadius.radiusSharp,
    ),
    pickupTheme: const PickupTheme(
      bagWithProductsTheme: BagWithProductsTheme(
        addressCardTheme: AddressCardTheme(borderRadius: 0.0),
      ),
    ),
    clockTheme: const ClockTheme(layoutBuilder: MariaFiloClockLayout.new),
    orderStatusTheme: OrderStatusTheme(
      colors: _colors,
      overrides: {
        OrderStatus.approvedPayment: OrderStatusLabel(
          label: 'preparando pedido',
          color: _colors.neutral.light2,
        ),
        OrderStatus.paymentPending: OrderStatusLabel(
          label: 'preparando pedido',
          color: _colors.neutral.light2,
        ),
      },
    ),
    selectorTheme: const SelectorTheme(
      isSquare: true,
      borderRadius: BorderRadius.zero,
      borderRadiusImage: BorderRadius.zero,
    ),
    orderConfirmationTheme: const OrderConfirmationTheme(
      detailsTheme: ConfirmedOrderDetailsTheme(
        showDeliverySLAAfterOrderDetails: true,
        showOrderIDInHeader: true,
        showOrderTrackingsOnlyInTopPage: false,
      ),
    ),
    pdpFullLookTheme: PDPFullLookTheme(
      buttonSize: ButtonSize.large,
      subtitleToProductListSpacing: _spacingStack.xl,
      productListToButtonSpacing: _spacingStack.xxl,
      bottomButtonSpacing: _spacingStack.xxxl,
      hasBottomDivider: true,
      changeSizeBottomSheetDesign: FullLookChangeSizeBottomSheetDesign.simple,
      bottomSheetAddToBagTheme: const PDPFullLookBottomSheetAddToBagTheme(
        productCardSizeButtonHasBackground: false,
      ),
      productCardTheme: ProductCardTheme(
        forceTextLink: true,
        isFullLook: false,
        widthSizeButton: 40,
        heightSizeButton: 40,
        titleToButtonsPadding: _spacingStack.lg,
        buttonsToLinkPadding: _spacingStack.lg,
      ),
    ),
    loginBottomSheetTheme: const LoginBottomSheetTheme(
      headerPadding: EdgeInsets.zero,
      titlePadding: EdgeInsets.zero,
    ),
    checkoutResumeTheme: CheckoutResumeTheme(
      backgrondColorMessageErrorWithPaymentCreditCard:
          _colors.feedback.pureError,
      colorMessageErrorWithPaymentCreditCard: _colors.neutral.pure1,
      colorCreditCardWithErrorWithPayment: _colors.feedback.pureError,
    ),
    paymentMethodTheme: const PaymentMethodTheme(
      pixInstallmentsHintBackgroundColor: Color(0xFFEAE9EA),
    ),
    cmsTheme: const CMSTheme(hasTipBar: true),
  );

  return mariaFiloDesignTokens;
}

const _colors = DTColors(
  brand: BrandColors(
    pure1: Color(0xFF2D2D30),
    pure2: Color(0xFFECE6E0),
    pure3: Color(0xFFECE6E0),
    pure4: Color(0xFFFFFFFF),
  ),
  typography: DTColorTypography(
    pure1: Color(0xFFFFFFFF),
    light2: Color(0xFF757575),
    pure2: Color(0xFF2D2D30),
  ),
  neutral: NeutralColor(
    pure1: Color(0xFFFFFFFF),
    light1: Color(0xFFF1F1F1),
    medium1: Color(0xFFCCCCCC),
    light2: Color(0xFF757575),
    pure2: Color(0xFF2D2D30),
  ),
  feedback: FeedBackColors(
    pureError: Color(0xFF8C4146),
    lightError: Color(0xFFFFF5F5),
    mediumError: Color(0XFF8C4146),
    pureSuccess: Color(0XFF3E533B),
    lightSuccess: Color(0XFFF0FAEF),
    mediumSuccess: Color(0XFF3E533B),
  ),
);

const _spacingStack = DTSpacingStack();

const _fontFamilyPrimary = "Lato";
const _fontSizes = FontSizes(xxl: 56, xxxus: 10);
const _lineHeight = LineHeight(
  ul: 96,
  xxxl: 72,
  xxl: 64,
  xl: 72,
  lg: 48,
  sm: 20,
  xs: 18,
  xxs: 24,
  us: 16,
  xus: 12,
  xxus: 10,
);

const _borderRadius = DTBorderRadius.squared(
  radiusLabel: 0,
  radiusSmall: 0,
);

const _icons = mariaFiloIconTokens;

final _pdpTheme = PdpTheme(
  productInformationTheme: const ProductInformationTheme(
    layout: ProductInformationLayout.columnBased,
  ),
  shareButton: const ShareButton(
    enabled: true,
    borderColor: Colors.transparent,
    borderRadius: 0.0,
  ),
  bottomBarBuyButtonWidth: 120,
  bottomBarBuyTextContainerWidth: 180,
  showBottomBarAtHeight: (Get.height / 2),
  productDetails: const MariaFiloProductDetails(),
  belowSelectSizeActions: [BelowSelectSizeActions.measurements],
  productTagsTheme: ProductTagsTheme(
    prismicTags: true,
    customTags: [
      ProductCustomTag<Product>(
        type: ProductCustomTagType.cluster,
        builder: (product) {
          try {
            final productClusters = product.productClusters!.values.toList();
            if (productClusters.any((cluster) =>
                (cluster as String).toLowerCase().contains('suite-tag'))) {
              return SMTag.cluster(
                text: 'SUITE',
                tagHeight: TagHeight.large,
              );
            }
          } catch (err) {
            return null;
          }
          return null;
        },
      ),
      ProductCustomTag(
        type: ProductCustomTagType.cluster,
        builder: <Product>(product) {
          try {
            final productClusters = product.productClusters!.values.toList();
            if (productClusters.any((cluster) => (cluster as String)
                .toLowerCase()
                .contains('novidades-da-semana'))) {
              SMTag.cluster(
                text: 'NEW',
                tagHeight: TagHeight.large,
              );
            }
          } catch (err) {
            return null;
          }
        },
      )
    ],
  ),
  forbiddenImageLabel: ["IMAGEM10"],
);

final _spotProductTheme = SpotProductTheme(
  imageHeight: SpotHeight.small,
  containerHeight: 375.0,
  productTagsTheme: ProductTagsTheme(
    prismicTags: true,
    productTagPlacement: ProductTagPlacement.start,
    customTags: [
      ProductCustomTag<Product>(
        type: ProductCustomTagType.cluster,
        builder: (product) {
          try {
            final productClusters = product.productClusters!.values.toList();
            if (productClusters.any((cluster) =>
                (cluster as String).toLowerCase().contains('suite-tag'))) {
              return SMTag.cluster(text: 'SUITE');
            }
          } catch (err) {
            return null;
          }
          return null;
        },
      ),
      ProductCustomTag(
        type: ProductCustomTagType.cluster,
        builder: <Product>(product) {
          try {
            final productClusters = product.productClusters!.values.toList();
            if (productClusters.any((cluster) => (cluster as String)
                .toLowerCase()
                .contains('novidades-da-semana'))) {
              return SMTag.cluster(text: 'NEW');
            }
          } catch (err) {
            return null;
          }
          return null;
        },
      )
    ],
  ),
);
