{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "azzas_analytics",
      "cwd": "azzas_analytics",
      "request": "launch",
      "type": "dart"
    },
    {
      "name": "azzas_analytics (profile mode)",
      "cwd": "azzas_analytics",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile"
    },
    {
      "name": "azzas_analytics (release mode)",
      "cwd": "azzas_analytics",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release"
    },
    {
      "name": "azzas_app_commons",
      "cwd": "azzas_app_commons",
      "request": "launch",
      "type": "dart"
    },
    {
      "name": "azzas_app_commons (profile mode)",
      "cwd": "azzas_app_commons",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile"
    },
    {
      "name": "azzas_app_commons (release mode)",
      "cwd": "azzas_app_commons",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release"
    },
    {
      "name": "azzas_cms",
      "cwd": "azzas_cms",
      "request": "launch",
      "type": "dart"
    },
    {
      "name": "azzas_cms (profile mode)",
      "cwd": "azzas_cms",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile"
    },
    {
      "name": "azzas_cms (release mode)",
      "cwd": "azzas_cms",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release"
    },
    {
      "name": "azzas_core",
      "cwd": "azzas_core",
      "request": "launch",
      "type": "dart"
    },
    {
      "name": "azzas_core (profile mode)",
      "cwd": "azzas_core",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile"
    },
    {
      "name": "azzas_core (release mode)",
      "cwd": "azzas_core",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release"
    },
    {
      "name": "azzas_ui",
      "cwd": "azzas_ui",
      "request": "launch",
      "type": "dart"
    },
    {
      "name": "azzas_ui (profile mode)",
      "cwd": "azzas_ui",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile"
    },
    {
      "name": "azzas_ui (release mode)",
      "cwd": "azzas_ui",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release"
    },
    {
      "name": "crisbarros_app_flutter",
      "cwd": "crisbarros_app_flutter",
      "request": "launch",
      "type": "dart"
    },
    {
      "name": "crisbarros_app_flutter (profile mode)",
      "cwd": "crisbarros_app_flutter",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile"
    },
    {
      "name": "crisbarros_app_flutter (release mode)",
      "cwd": "crisbarros_app_flutter",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release"
    }
  ]
}