class ContentDevolutionConfig {
  final bool? showContent;
  final bool? showBottomSheet;

  const ContentDevolutionConfig({
    this.showContent,
    this.showBottomSheet,
  });

  factory ContentDevolutionConfig.fromJson(Map<String, dynamic> json) {
    final teste = ContentDevolutionConfig(
      showContent: json['show_content'],
    );
    return teste;
  }

  ContentDevolutionConfig copyWith({
    bool? showContent,
  }) {
    return ContentDevolutionConfig(
      showContent: showContent ?? this.showContent,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'show_content': showContent,
    };
  }

  ContentDevolutionConfig applyTo(ContentDevolutionConfig config) {
    return ContentDevolutionConfig(
      showContent: showContent,
    );
  }
}
