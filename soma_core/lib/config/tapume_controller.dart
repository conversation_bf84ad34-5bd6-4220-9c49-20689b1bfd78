import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:soma_core/soma_core.dart';

import '../modules/catalog/controllers/catalog_controller.dart';

class TapumeController extends FullLifeCycleController with FullLifeCycleMixin {
  TapumeController(this.catalogController);

  final CatalogController catalogController;
  var tapume = const Tapume();
  var tapumeEtc = const Tapume();
  final tapumeRoute = '/tapume';
  final webviewRoute = '/webview';
  bool shouldGoToTapume = false;

  @override
  void onInit() async {
    super.onInit();
    try {
      tapume = await catalogController.getTapume();
      shouldGoToTapume = tapumeIsActive;
      if (catalogController.brand == Brand.farm) {
        tapumeEtc = await catalogController.getTapumeEtc();
      }
    } catch (e) {
      debugPrint("Error on get tapume - $e");
    }
  }

  bool get tapumeIsActive {
    final tapumeIsActiveCountdown = tapume.isActive == true &&
        tapume.isCountdown == true &&
        tapume.isClockActive;
    final tapumeIsActiveDefault =
        tapume.isActive == true && tapume.isCountdown == false;

    return tapumeIsActiveCountdown || tapumeIsActiveDefault;
  }

  @override
  void onResumed() async {}

  @override
  void onDetached() {}

  @override
  void onInactive() async {
    try {
      var currentRoute = Get.currentRoute;

      tapume = await catalogController.getTapume();
      if (tapume.isActive) {
        return goToTapume();
      }

      if (currentRoute == tapumeRoute) {
        Get.offAllNamed('/');
      }
    } catch (_) {}
  }

  @override
  void onPaused() {}

  void goToTapume() async {
    if (tapume.urlRedirect != null &&
        tapume.urlRedirect!.contains("webview/")) {
      Get.offAllNamed(webviewRoute,
          arguments: {"url": tapume.urlRedirect!.replaceAll("webview/", "")});
    } else {
      Get.offAllNamed(tapumeRoute);
    }
  }

  @override
  void onHidden() {
    // TODO: implement onHidden
  }
}
