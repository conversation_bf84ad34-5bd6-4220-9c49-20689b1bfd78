enum CountdownTheme {
  light,
  dark,
}

class Tapume {
  final CmsImageData? image;
  final String? urlRedirect;
  final bool isActive;
  final DateTime? startTime;
  final DateTime? endTime;
  final CountdownTheme countdownTheme;
  final bool isCountdown;

  const Tapume({
    this.image,
    this.urlRedirect,
    this.isActive = false,
    this.startTime,
    this.endTime,
    this.countdownTheme = CountdownTheme.dark,
    this.isCountdown = false,
  });

  factory Tapume.fromJson(Map<String, dynamic> json) {
    final dynamic imageData = json['image']?['data'];

    CmsImageData image;

    if (imageData is List && imageData.isNotEmpty) {
      image = CmsImageData.fromJson(imageData[0]);
    } else if (imageData is Map<String, dynamic>) {
      image = CmsImageData.fromJson(imageData);
    } else {
      image = const CmsImageData(
          id: 0,
          attributes: CmsImageDataAttributes(
              width: 0, height: 0, mime: '', url: '', name: ''));
    }

    return Tapume(
      image: image,
      urlRedirect: json['url_redirect'] as String?,
      isActive: json['active'] == true && json['image']['data'] != null
          ? true
          : false,
      startTime: json['start_time'] != null
          ? DateTime.parse(json['start_time'])
          : null,
      endTime:
          json['end_time'] != null ? DateTime.parse(json['end_time']) : null,
      countdownTheme:
          json['theme'] == 'dark' ? CountdownTheme.dark : CountdownTheme.light,
      isCountdown: json['is_countdown'] == true ? true : false,
    );
  }

  bool get isClockActive {
    if (startTime == null || endTime == null) {
      return false;
    }

    return DateTime.now()._isBetween(startTime!, endTime!);
  }
}

class CmsImageData {
  const CmsImageData({
    required this.id,
    required this.attributes,
  });

  factory CmsImageData.fromJson(Map<String, dynamic>? json) {
    return CmsImageData(
      id: json?['id'],
      attributes: CmsImageDataAttributes.fromJson(json?['attributes']),
    );
  }

  final int id;
  final CmsImageDataAttributes attributes;
}

class CmsImageDataAttributes {
  const CmsImageDataAttributes({
    required this.width,
    required this.height,
    required this.mime,
    required this.url,
    required this.name,
  });
  final int? width;
  final int? height;
  final String mime;
  final String url;
  final String name;

  factory CmsImageDataAttributes.fromJson(Map<String, dynamic>? json) {
    return CmsImageDataAttributes(
      width: json?['width'],
      height: json?['height'],
      mime: json?['mime'],
      url: json?['url'],
      name: json?['name'],
    );
  }
}

extension on DateTime {
  bool _isBetween(DateTime start, DateTime end) {
    return (isAfter(start) || isAtSameMomentAs(start)) &&
        (isBefore(end) || isAtSameMomentAs(end));
  }
}
