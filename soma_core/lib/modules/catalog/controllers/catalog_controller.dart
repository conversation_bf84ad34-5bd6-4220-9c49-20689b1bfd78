import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:soma_core/modules/checkout/models/index.dart';
import 'package:soma_core/soma_core.dart';
import 'package:soma_analytics/controller/soma_analytics_controller.dart';

import '../models/autocomplete/autocomplete_item/autocomplete_item.dart';
import '../models/autocomplete/autocomplete_item/autocomplete_product.dart';
import '../models/prismic/lp_custom_items_response.dart';
import '../models/product/status_order.dart';
import '../models/search/live_stream.dart';
import '../utils/stories_video_storage.dart';
import 'intelligent_search_controller.dart';

class CatalogController extends GetxController with StoriesVideoStorage {
  @Deprecated(
      'Prefira obter uma instância do IntelligentSearchController diretamente')
  final IntelligentSearchController intelligentSearchController;
  final SomaAnalyticsController analyticsController;
  final PrismicService prismicService;
  final StrapiService strapiService;
  final CatalogService service;
  final Config config;
  final HttpClient cmsHttpClient;
  final Brand brand;
  @override
  final KeyValueStorage storage;

  var similarProducts = Rxn<List<Product>>([]);
  var fullLookProducts = Rxn<List<Product>>([]);
  var isLoading = Rxn<bool>(false);
  var totalProducts = Rxn<int>(0);
  var selectedProduct = Rxn<Product>(null);
  var autocompleteProducts = Rx<List<AutocompleteProduct>>([]);
  var autocompleteSuggestions = Rx<List<AutocompleteItem>>([]);
  var productSuggestions = Rx<List<ProductSuggestion>?>(null);

  final liveStream = Rxn<LiveStream>(null);
  RxBool isLiveStreamLoading = RxBool(false);

  CatalogController(
    this.service,
    this.strapiService,
    this.prismicService,
    this.config,
    this.analyticsController,
    this.intelligentSearchController,
    this.storage,
    this.cmsHttpClient,
    this.brand,
  );

  Future<SkuStock> getStockBySkuId({
    required String skuId,
  }) async {
    try {
      var response = await service.getStockBySku(skuId: skuId);
      return response;
    } catch (e) {
      debugPrint(e.toString());
      rethrow;
    }
  }

  Future<List<Product>> getProductsByCategoryIdOrClusterId({
    required bool isCollection,
    required String searchId,
    String? orderBy,
  }) async {
    List<Product> products = [];
    try {
      var orderByFormatted =
          orderBy != null ? Search.getOrderByString(orderBy) : null;

      if (isCollection) {
        products = await getProductsByClusterId(
            clusterId: int.parse(searchId), orderBy: orderByFormatted);
      } else {
        products = await getProductsByCategoryId(
            categoryId: searchId, orderBy: orderByFormatted);
      }
      return products;
    } catch (e) {
      debugPrint(e.toString());
      rethrow;
    }
  }

  void updateSelectedProduct(Product product) {
    selectedProduct(product);
  }

  Future<List<int>> getFullLookProductsId(int? productId) async {
    try {
      List<int> tempProductIds = [];
      isLoading(true);
      fullLookProducts([]);
      var fullLook = <ProdutcOrderForm>[];
      if (productId != null) {
        var response = await service.getFullLookProducts(productId);
        for (var product in response) {
          var duplicated =
              fullLook.indexWhere((e) => e.productId == product.productId) > -1;

          if (!duplicated) {
            fullLook.add(product);
          }
        }
      }

      for (var product in fullLook) {
        if (product.id != null) {
          tempProductIds.add(int.parse(product.productId!));
        }
      }

      return tempProductIds;
    } catch (e, st) {
      debugPrint(e.toString());
      debugPrintStack(stackTrace: st);
      isLoading(false);
      throw SomaCoreException(
          message: 'um erro ocorreu na busca do seu produto, tente novamente!',
          cause: e);
    } finally {
      isLoading(false);
    }
  }

  Future<String> getProductColorFromMasterData(String referenceId) async {
    final color = await service.getProductColorFromMasterData(
      referenceId,
      config.store.storeUrl,
    );

    return color;
  }

  Future<void> getSimilarProduct({int? productId, String? referenceId}) async {
    try {
      isLoading(true);
      similarProducts([]);
      var similar = <Product>[];
      if (productId != null) {
        var response = await service.getSimilarProduct(productId);
        for (var item in response) {
          var duplicated =
              similar.indexWhere((e) => e.productId == item.productId) > -1;

          if (!duplicated &&
              _isAvailableInDefaultSeller(item) &&
              (item.imageSku.isNotEmpty ||
                  config.appFeaturesConfig.useProductColorInThumb)) {
            similar.add(item);
          }
        }
      } else if (referenceId != null || (referenceId?.isEmpty ?? false)) {
        var response =
            await service.getSimilarProductsByRefId(referenceId ?? '');
        for (var item in response) {
          var duplicated =
              similar.indexWhere((e) => e.productId == item.productId) > -1;

          if (!duplicated && _isAvailableInDefaultSeller(item)) {
            similar.add(item);
          }
        }
      }

      similarProducts(similar);
    } catch (e, st) {
      debugPrint(e.toString());
      debugPrintStack(stackTrace: st);
      isLoading(false);
      throw SomaCoreException(
          message: 'um erro ocorreu na busca do seu produto, tente novamente!',
          cause: e);
    } finally {
      isLoading(false);
    }
  }

  bool _isAvailableInDefaultSeller(Product item) {
    return item.firstAvailableSeller?.commertialOffer?.isAvailable ?? false;
  }

  Future<Product> getProductById({
    required int productId,
    bool loadSimilarProducts = true,
  }) async {
    isLoading(true);
    final params = Search(productId: productId);
    String message =
        'um erro ocorreu na busca do seu produto, tente novamente!';

    try {
      List<Product> response = await service.searchProducts(search: params);
      if (loadSimilarProducts) {
        await getSimilarProduct(productId: productId);
      }

      if (response.isNotEmpty) {
        isLoading(false);
        return response[0];
      }

      message =
          'produto com o productId: $productId não encontrado, tente novamente!';

      throw SomaCoreException(message: message);
    } catch (e) {
      debugPrint(e.toString());

      isLoading(false);
      throw SomaCoreException(message: message, cause: e);
    }
  }

  Future<List<Product>> getProductsById(
      {required List<int> productsIds}) async {
    isLoading(true);
    try {
      return await service.getProductsById(productsIds);
    } catch (e) {
      debugPrint(e.toString());
      throw SomaCoreException(
        message:
            'não foi possível encontrar os produtos ${productsIds.join(', ')}',
        cause: e,
      );
    } finally {
      isLoading(false);
    }
  }

  Future<List<Product>> getProductsByCategoryId(
      {required String categoryId, OrderBy? orderBy}) async {
    try {
      isLoading(true);
      final params = Search(categoryId: categoryId, orderBy: orderBy);
      List<Product> response = await service.searchProducts(search: params);
      isLoading(false);

      return response;
    } catch (e, st) {
      debugPrint(e.toString());
      debugPrintStack(stackTrace: st);

      isLoading(false);
      throw SomaCoreException(
        message: 'um erro ocorreu na busca do seu produto, tente novamente!',
        cause: e,
      );
    }
  }

  Future<List<Product>> getProductsByClusterId(
      {required int clusterId, OrderBy? orderBy}) async {
    try {
      isLoading(true);
      final params = Search(clusterId: clusterId, orderBy: orderBy);
      List<Product> response = await service.searchProducts(search: params);
      isLoading(false);

      return response;
    } catch (e, st) {
      debugPrint(e.toString());
      debugPrintStack(stackTrace: st);

      isLoading(false);
      throw SomaCoreException(
        message: 'um erro ocorreu na busca do seu produto, tente novamente!',
        cause: e,
      );
    }
  }

  Future<List<Product>> executeSearch(Search search) async {
    try {
      final response = await service.searchProducts(search: search);
      return response;
    } catch (e) {
      debugPrint('Erro ao executar busca de produtos: $e');
      throw SomaCoreException(
        message: 'um erro ocorreu na busca do seu produto, tente novamente!',
        cause: e,
      );
    }
  }

  Future<void> getAutocomplete(String searchString) async {
    isLoading(true);
    try {
      List<AutocompleteProduct> tempAutocompleteProducts = [];
      List<AutocompleteItem> tempAutocompleteSuggestions = [];

      final autocompleteResponse = await service.getAutocomplete(searchString);

      for (var autocompleteItem in autocompleteResponse.itemsReturned ?? []) {
        if (autocompleteItem.criteria == null) {
          tempAutocompleteProducts.add(autocompleteItem.items![0]);
        } else {
          tempAutocompleteSuggestions.add(autocompleteItem);
        }
      }

      autocompleteProducts.value = tempAutocompleteProducts;
      autocompleteSuggestions.value = tempAutocompleteSuggestions;
    } catch (e, st) {
      debugPrint(e.toString());
      debugPrintStack(stackTrace: st);
    } finally {
      isLoading(false);
    }
  }

  Future<void> getProductSuggestions(String searchString) async {
    isLoading(true);
    try {
      final productSuggestionsResponse =
          await service.getProductSuggestions(searchString);

      productSuggestions.value = productSuggestionsResponse.productSuggestions;
    } catch (e, st) {
      debugPrint(e.toString());
      debugPrintStack(stackTrace: st);
    } finally {
      isLoading(false);
    }
  }

  Future<void> getTotalProductsByClusterId({required Search search}) async {
    try {
      totalProducts(await service.totalProducts(search: search));
    } catch (e, st) {
      debugPrint(e.toString());
      debugPrintStack(stackTrace: st);
      isLoading(false);
      throw SomaCoreException(
        message: 'um erro ocorreu na busca do seu produto, tente novamente!',
        cause: e,
      );
    }
  }

  Future<void> clearPrismicCache() {
    return prismicService.clearCache();
  }

  Future<List<StatusOrder>>? getStatusByOrderId(
      String orderId, bool isPickup) async {
    try {
      return await service.getStatusByOrderId(orderId, isPickup);
    } catch (e, st) {
      debugPrint(e.toString());
      debugPrintStack(stackTrace: st);

      isLoading(false);
      throw SomaCoreException(
        message: 'um erro ocorreu na busca do seu produto, tente novamente!',
        cause: e,
      );
    }
  }

  Future<List<Product>> getFilteredProductsByRefId(
      {required String refIds}) async {
    try {
      return await service.getFilteredProductsByRefsIds(refIds);
    } catch (e, st) {
      debugPrint(e.toString());
      debugPrintStack(stackTrace: st);
      throw SomaCoreException(
          message:
              'ocorreu um erro ao buscar os produtos similares, tente novamente!',
          cause: e);
    }
  }

  Future<List<Body>> getHomeItems() async {
    return await prismicService.getItems('home');
  }

  Future<List<Body>> getClockPromotionItems() async {
    return await prismicService.getItems('clock_promotion');
  }

  Future<List<Body>> getEmptyWishlistItems() async {
    return await prismicService.getItems('empty_wishlist');
  }

  Future<List<Body>> getBagItems() async {
    return await prismicService.getItems('bag');
  }

  Future<List<Body>> getEmptyBagItems() async {
    return await prismicService.getItems('emptybag');
  }

  Future<List<Body>> getEmptyOrderItems() async {
    return await prismicService.getItems('empty_orders');
  }

  Future<List<Body>> getPdpItems() async {
    return await prismicService.getItems('pdp');
  }

  Future<List<Body>> getGiftCardPdpItems() async {
    return await prismicService.getItems('gift_card_pdp');
  }

  Future<List<Body>> getEmptySearchItems() async {
    return await prismicService.getItems('empty_search');
  }

  Future<List<Body>> getSearchTrendsItems() async {
    return await prismicService.getItems('search_trends');
  }

  Future<List<Body>> getTipBarItems() async {
    return await prismicService.getItems('tip_bar');
  }

  Future<List<Body>> getCashBackFaq() async {
    return await prismicService.getItems('cashback_faq');
  }

  Future<List<Body>> getCouponsItems() async {
    return await prismicService.getItems('coupon');
  }

  Future<LpCustomItemResponse?> getLpCustomItems(String id) async {
    return await prismicService.getLpCustomItems(id);
  }

  bool listHasLiveStream(Search currentSearch) {
    final liveStreamConfig = config.liveStream;
    final term = currentSearch.filterCategoryOrCluster ?? '';
    final isAvaliableLiveStream = liveStreamConfig != null
        ? RegExp(liveStreamConfig.categoryOrClusterRegex).hasMatch(term)
        : false;
    return isAvaliableLiveStream;
  }

  Future<void> getLiveStream() async {
    try {
      isLiveStreamLoading(true);
      final result = await service.getLiveStream();
      liveStream.value = result;
    } catch (err, stack) {
      analyticsController.logError(err, stack);
    } finally {
      isLiveStreamLoading(false);
    }
  }

  Future<Tapume> getTapume() async {
    try {
      final response = await cmsHttpClient.get(
        'tapume-${brand.name.toLowerCase()}',
        queryParameters: {
          'populate': 'deep,5',
        },
      );
      var tapume;

      if (response.data != null &&
          response.data['data'] is List &&
          (response.data['data'] as List).isNotEmpty) {
        tapume = response.data['data'][0]?['attributes'];
      }

      if (tapume != null) {
        return Tapume.fromJson(tapume);
      }

      return const Tapume();
    } catch (e) {
      debugPrint(e.toString());
      if (e is Error) {
        debugPrintStack(stackTrace: e.stackTrace);
      }
      rethrow;
    }
  }

  Future<Tapume> getTapumeEtc() async {
    try {
      final response = await cmsHttpClient.get(
        'tapume-${brand.name.toLowerCase()}-etc',
        queryParameters: {
          'populate': 'deep,5',
        },
      );

      var tapumeEtc;

      if (response.data != null &&
          response.data['data'] is List &&
          (response.data['data'] as List).isNotEmpty) {
        tapumeEtc = response.data['data'][0]?['attributes'];
      }

      if (tapumeEtc != null) {
        return Tapume.fromJson(tapumeEtc);
      }

      return const Tapume();
    } catch (e) {
      debugPrint(e.toString());
      if (e is Error) {
        debugPrintStack(stackTrace: e.stackTrace);
      }
      rethrow;
    }
  }

  Future<String?> getBgColor(Product product) async {
    try {
      String color =
          await strapiService.getBgColor(StrapiProductColorRequestDto(
        productId: product.productId!,
        productName: product.productName!,
        printUrl: product.printImage!.imageUrl!,
        imageUrl: product.coverImage,
        storeId: config.store.storeName,
      ));
      return color;
    } catch (err, stack) {
      analyticsController.logError(err, stack);
      return null;
    }
  }

  Future<List<Body>> getSuggestedSuitcase() async {
    return await prismicService.getItems("suggested_suitcase");
  }

  Future<List<Body>> getCreatedSuitcase() async {
    return await prismicService.getItems("created_suitcase");
  }

  Future<List<Product>> searchProductSimilar(Product product) async {
    try {
      var similar = <Product>[];

      if (product.productId != null) {
        var productId = int.parse(product.productId.toString());
        var response = await service.getSimilarProduct(productId);
        for (var item in response) {
          var duplicated =
              similar.indexWhere((e) => e.productId == item.productId) > -1;

          if (!duplicated &&
              _isAvailableInDefaultSeller(item) &&
              (item.imageSku.isNotEmpty ||
                  config.appFeaturesConfig.useProductColorInThumb)) {
            similar.add(item);
          }
        }
      }

      return similar;
    } catch (e, st) {
      debugPrint(e.toString());
      debugPrintStack(stackTrace: st);

      return [];
    }
  }
}
