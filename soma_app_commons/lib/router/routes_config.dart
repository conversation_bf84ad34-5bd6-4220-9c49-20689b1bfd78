import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:soma_ui/soma_ui.dart';
import 'package:soma_ui/widgets/templates/checkout/payment/promotions_engine/sm_credit_voucher.dart';
import 'package:soma_ui/widgets/templates/sm_full_look_page.dart';
import '../screens/screens.dart';

class RoutesConfig {
  Map<String, Widget Function()> createDefaultRoutes(AppRoutes routes) {
    return {
      routes.productDetails: () => const ProductDetail(),
      routes.webview: () => const WebView(),
      routes.searchResult: () => const SearchResult(),
      routes.orders: () => const Orders(),
      routes.trackOrder: () => const TrackOrder(),
      routes.addCoupons: () => const CouponsApplied(),
      routes.paymentsInstallments: () => const Installments(),
      routes.paymentMethod: () => const PaymentMethod(),
      routes.paymentNewCreditCard: () => const NewCreditCard(),
      routes.shippingRegistration: () => const ShippingRegistration(),
      routes.addressRegistration: () => const AddressRegistration(),
      routes.confirmationOrder: () => const ConfirmationOrder(),
      routes.orderReview: () => const OrderReview(),
      routes.checkout: () => const Bag(),
      routes.filters: () => const Filters(),
      routes.userData: () => const UserData(),
      routes.wishlist: () => const Wishlist(),
      routes.explore: () => const ExploreMenu(),
      routes.emptySearch: () => const EmptySearch(),
      routes.genericError: () => const GenericError(),
      routes.accountDelete: () => const AccountDelete(),
      routes.tapume: () => const SmTapume(),
      routes.creditVoucher: () => const SMCreditVoucher(),
      routes.videoContent: () => const SMVideoContent(),
      routes.combos: () => const Combos(),
      routes.callCenter: () => const CallCenter(),
      routes.suggestions: () => const SmSuggetionsPage(),
      routes.fullLook: () => const SMFullLookPage(),
      routes.liveTransmission: () => const LiveTransmissionPage(),
    };
  }

  List<GetPage> createPages(
      Map<String, Widget Function()> pagesDefinition, AppRoutes routes) {
    return pagesDefinition.entries
        .map((e) => GetPage(
            name: e.key,
            page: e.value,
            transition: _getTransitionForRoute(e.key, routes)))
        .toList();
  }
}

Transition _getTransitionForRoute(String routeName, AppRoutes routes) {
  if (routeName == routes.cpfCheck) {
    return Transition.rightToLeft;
  }

  return Transition.noTransition;
}
