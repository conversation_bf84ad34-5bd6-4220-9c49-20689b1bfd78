import 'package:delivery_promises/src/controllers/delivery_promises_controller.dart';
import 'package:soma_core/modules/catalog/catalog.dart';
import 'package:soma_core/modules/catalog/controllers/intelligent_search_controller.dart';

/// Plugin que tem a responsabilidade de transformar a busca do IntelligentSearch utilizando as
/// informações salvas no [DeliveryPromisesController].
class DeliveryPromisesSearchTransformerPlugin
    extends DynamicSearchTransformerPlugin {
  final DeliveryPromisesController _controller;

  DeliveryPromisesSearchTransformerPlugin(this._controller);

  static const _intelligentSearchType = IntelligentSearchType.deliveryPromises;

  @override
  String get name => 'DeliveryPromisesSearchTransformer';

  @override
  IntelligentSearchVariation getSearchVariationParams() {
    final address = _controller.savedAddress.value;
    final postalCode = address?.postalCode;
    final shippingOption = _controller.selectedShippingOption.value;

    if (shippingOption == null) {
      return IntelligentSearchVariation.empty;
    }

    var pathSegments = 'zip-code/$postalCode/shipping/${shippingOption.path}';

    if (shippingOption == ShippingOptionType.pickupSingleStore) {
      final store = _controller.selectedPickupStore.value;

      if (store != null && store.pickupPoint.id != null) {
        pathSegments += '/pickupPoint/${store.pickupPoint.id}/';
      }
    }

    return IntelligentSearchVariation(
      pathSegments: pathSegments,
      type: _intelligentSearchType,
      queryParams: {
        'formatAsBefore': 'true',
        'simulationBehavior': 'skip',
      },
    );
  }
}
