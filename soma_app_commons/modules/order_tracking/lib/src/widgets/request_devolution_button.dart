import 'package:flutter/material.dart';
import 'package:soma_ui/soma_ui.dart';
import 'package:auto_size_text/auto_size_text.dart';

class RequestDevolutionButton extends StatefulWidget {
  const RequestDevolutionButton({
    Key? key,
    required this.orderId,
  }) : super(key: key);

  final String orderId;

  @override
  State<RequestDevolutionButton> createState() =>
      _RequestDevolutionButtonState();
}

class _RequestDevolutionButtonState extends State<RequestDevolutionButton>
    with AppRoutesStateMixin, DesignTokensStateMixin, SomaCoreStateMixin {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).extension<RequestDevolutionButtonTheme>();

    return SMButton.secondary(
      expanded: true,
      style: SMButtonStyle(
        borderColor: colors.neutral.medium1,
        padding: EdgeInsets.symmetric(
          vertical: spacingStack.sm,
          horizontal: spacingInline.sm,
        ),
      ).merge(theme?.style),
      size: ButtonSize.medium,
      onPressed: () {
        if ((config.contentDevolutionConfig?.showContent ?? false) ||
            (config.contentDevolutionConfig?.showBottomSheet ?? false)) {
          customShowBottomSheet(BottomSheetDevolution(
            orderId: widget.orderId,
            showCloseText: false,
          ));
        } else {
          navigateToNamed(
            webviewRoute,
            arguments: WebViewParams(
              url: config.appUrls.devolutionRouteUrlTemplate
                  .replaceAll('{orderId}', widget.orderId)
                  .replaceAll('{userEmail}',
                      authController.localUserInfo!.personEmail!),
              screenName: 'Devolução',
              allowJavascript: true,
            ),
          );
          context.dispatchSelectContentEvent(
              'meus-pedidos:solicitar-troca-ou-devolucao');
        }
      },
      child: Builder(builder: (context) {
        final defaultTextStyle = DefaultTextStyle.of(context).style;
        return AutoSizeText(
          textTransform.button(
            termsAndMessages.orderTrackingRequestDevolutionButtonTitle,
          ),
          overflowReplacement: Text(textTransform.button('devolução')),
          maxLines: 1,
          softWrap: false,
          minFontSize: defaultTextStyle.fontSize ?? 14,
          maxFontSize: defaultTextStyle.fontSize ?? 14,
        );
      }),
    );
  }
}

class RequestDevolutionButtonTheme
    extends ThemeExtension<RequestDevolutionButtonTheme> {
  const RequestDevolutionButtonTheme({
    this.style,
  });

  final SMButtonStyle? style;

  @override
  RequestDevolutionButtonTheme copyWith({
    SMButtonStyle? style,
  }) {
    return RequestDevolutionButtonTheme(
      style: style ?? this.style,
    );
  }

  @override
  RequestDevolutionButtonTheme lerp(
    RequestDevolutionButtonTheme? other,
    double t,
  ) {
    return RequestDevolutionButtonTheme(
      style: t < 0.5 ? style : other?.style,
    );
  }
}
