import 'package:flutter/material.dart';
import 'package:soma_core/soma_core.dart';
import 'package:soma_ui/soma_ui.dart';

import '../controller/sm_measurement_guide_controller.dart';
import 'index.dart';

class SmProductMeasurements extends StatefulWidget {
  final SmMeasurementGuideController measurementGuideController;
  final Product product;

  const SmProductMeasurements(
      {Key? key,
      required this.measurementGuideController,
      required this.product})
      : super(key: key);

  @override
  State<SmProductMeasurements> createState() => _SmProductMeasurementsState();
}

class _SmProductMeasurementsState extends State<SmProductMeasurements>
    with DesignTokensStateMixin, SomaCoreStateMixin {
  @override
  void initState() {
    super.initState();

    widget.measurementGuideController.initMeasurementGuide(widget.product);
  }

  @override
  Widget build(BuildContext context) {
    final itemSizeWhichHasMeasurements =
        widget.product.productMeasurements.map((e) => e.size).toList();

    return SmObserver(builder: (_) {
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: spacingStack.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Selecione um tamanho',
              style: typography.typeStyles.subtitle.copyWith(
                  fontWeight: FontWeight.w400, color: colors.typography.pure2),
            ),
            SizedBox(
              height: spacingStack.md,
            ),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  ...widget.product.items!.map(
                    (e) {
                      return itemSizeWhichHasMeasurements.contains(e.itemSize)
                          ? Padding(
                              padding: EdgeInsets.only(right: spacingStack.xs),
                              child: SMSizeButton(
                                textVariant: TextVariant.upperCase,
                                height: 40,
                                text: TextUtils.verifySizeText(e.itemSize),
                                isActive: widget.measurementGuideController.item
                                        .value.itemSize ==
                                    e.itemSize,
                                onTap: () => widget
                                    .measurementGuideController.item.value = e,
                              ),
                            )
                          : const SizedBox.shrink();
                    },
                  ),
                ],
              ),
            ),
            SizedBox(
              height: spacingStack.xl,
            ),
            ...widget.measurementGuideController.productMeasurements.value
                .measurements
                .map(
              (e) {
                return SmMeasurementItem(label: e.label, value: e.value);
              },
            ),
            Text(
              'As medidas podem ter variações de até 2cm',
              style: typography.typeStyles.subtitle.copyWith(
                  color: colors.typography.pure2, fontWeight: FontWeight.w400),
            ),
            SizedBox(
              height: spacingStack.xl,
            ),
            Text(
              'Como medir o tamanho do produto:',
              style: typography.typeStyles.subtitle.copyWith(
                  color: colors.typography.pure2, fontWeight: FontWeight.w400),
            ),
            SizedBox(
              height: spacingStack.xxl,
            ),
            SMImage(
              fit: BoxFit.contain,
              imageHeight: 290,
              imageWidth: double.infinity,
              image: NetworkImage(
                '${config.store.storeUrl}/${widget.product.measuredPictures}'.cleanUrl(),
              ),
            ),
            const SizedBox(
              height: 180,
            )
          ],
        ),
      );
    });
  }
}
