# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: ae92f5d747aee634b87f89d9946000c2de774be1d6ac3e58268224348cd0101a
      url: "https://pub.dev"
    source: hosted
    version: "61.0.0"
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: "37a42d06068e2fe3deddb2da079a8c4d105f241225ba27b7122b37e9865fd8f7"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.35"
  adyen_checkout:
    dependency: transitive
    description:
      name: adyen_checkout
      sha256: "7747c9cbd1cb4aaaa11c1da9a7f345d0510d8a7a9a1e4af12e55b32bc9465434"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: ea3d8652bda62982addfd92fdc2d0214e5f82e43325104990d4f4c4a2a313562
      url: "https://pub.dev"
    source: hosted
    version: "5.13.0"
  app_settings:
    dependency: transitive
    description:
      name: app_settings
      sha256: "66715a323ac36d6c8201035ba678777c0d2ea869e4d7064300d95af10c3bb8cb"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.0"
  app_tracking_transparency:
    dependency: transitive
    description:
      name: app_tracking_transparency
      sha256: "7b011da3165ebb5a2f93d19da22e43a94b45bb6471c825b7d163b30eff937d30"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  appsflyer_sdk:
    dependency: transitive
    description:
      name: appsflyer_sdk
      sha256: "399a0e8ca793b0e7a394655aaf27d631d63a3a96d9d93136f75f764969896150"
      url: "https://pub.dev"
    source: hosted
    version: "6.12.2"
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: "7b875fd4a20b165a3084bd2d210439b22ebc653f21cea4842729c0c30c82596b"
      url: "https://pub.dev"
    source: hosted
    version: "3.4.9"
  args:
    dependency: transitive
    description:
      name: args
      sha256: eef6c46b622e0494a36c5a12d10d77fb4e855501a91c1b9ef9339326e58f0596
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "947bfcf187f74dbc5e146c9eb9c0f10c9f8b30743e341481c1e2ed3ecc18c20c"
      url: "https://pub.dev"
    source: hosted
    version: "2.11.0"
  battery_plus:
    dependency: transitive
    description:
      name: battery_plus
      sha256: "0568fbba70697b8d0c34c1176faa2bc6d61c7fb211a2d2d64e493b91ff72d3f8"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.2"
  battery_plus_platform_interface:
    dependency: transitive
    description:
      name: battery_plus_platform_interface
      sha256: "942707f90e2f7481dcb178df02e22a9c6971b3562b848d6a1b8c7cff9f1a1fec"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: "04a925763edad70e8443c99234dc3328f442e811f1d8fd1a72f1c8ad0f69a605"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  collection:
    dependency: "direct main"
    description:
      name: collection
      sha256: ee67cb0715911d28db6bf4af1026078bd6f0128b07a5f66fb2ed94ec6783c09a
      url: "https://pub.dev"
    source: hosted
    version: "1.18.0"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: "0f08b14755d163f6e2134cb58222dd25ea2a2ee8a195e53983d57c075324d592"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  coverage:
    dependency: transitive
    description:
      name: coverage
      sha256: "3945034e86ea203af7a056d98e98e42a5518fff200d6e8e6647e1886b07e936e"
      url: "https://pub.dev"
    source: hosted
    version: "1.8.0"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "445db18de832dba8d851e287aff8ccf169bed30d2e94243cb54c7d2f1ed2142c"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.3+6"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: ff625774173754681d66daaf4a448684fb04b78f902da9cb3d308c19cc5e8bab
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "831883fb353c8bdc1d71979e5b342c7d88acfbc643113c14ae51e2442ea0f20f"
      url: "https://pub.dev"
    source: hosted
    version: "0.17.3"
  cupertino_icons:
    dependency: transitive
    description:
      name: cupertino_icons
      sha256: ba631d1c7f7bef6b729a622b7b752645a2d076dba9976925b8f25725a30e1ee6
      url: "https://pub.dev"
    source: hosted
    version: "1.0.8"
  date_picker_plus:
    dependency: transitive
    description:
      name: date_picker_plus
      sha256: fda3fc7a2da8a9af1b7cf862c529143c98ceadb5d49870e2b28612c3f0d89422
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "365c771ac3b0e58845f39ec6deebc76e3276aa9922b0cc60840712094d9047ac"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.10"
  device_info_plus:
    dependency: transitive
    description:
      name: device_info_plus
      sha256: "77f757b789ff68e4eaf9c56d1752309bd9f7ad557cb105b938a7f8eb89e59110"
      url: "https://pub.dev"
    source: hosted
    version: "9.1.2"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      sha256: d3b01d5868b50ae571cd1dc6e502fc94d956b665756180f7b16ead09e836fd64
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  diacritic:
    dependency: transitive
    description:
      name: diacritic
      sha256: "96db5db6149cbe4aa3cfcbfd170aca9b7648639be7e48025f9d458517f807fe4"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.5"
  dio:
    dependency: "direct main"
    description:
      name: dio
      sha256: "0dfb6b6a1979dac1c1245e17cef824d7b452ea29bd33d3467269f9bef3715fb0"
      url: "https://pub.dev"
    source: hosted
    version: "5.6.0"
  dio_cache_interceptor:
    dependency: transitive
    description:
      name: dio_cache_interceptor
      sha256: fb7905c0d12075d8786a6b63bffd64ae062d053f682cfaf28d145a2686507308
      url: "https://pub.dev"
    source: hosted
    version: "3.5.0"
  dio_web_adapter:
    dependency: transitive
    description:
      name: dio_web_adapter
      sha256: "36c5b2d79eb17cdae41e974b7a8284fec631651d2a6f39a8a2ff22327e90aeac"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  dropdown_button2:
    dependency: transitive
    description:
      name: dropdown_button2
      sha256: b0fe8d49a030315e9eef6c7ac84ca964250155a6224d491c1365061bc974a9e1
      url: "https://pub.dev"
    source: hosted
    version: "2.3.9"
  eraser:
    dependency: transitive
    description:
      name: eraser
      sha256: "89bbfceda3c0979710c4849f1735b0c33af7fea4504042692285b6c530ac4f28"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  expandable_page_view:
    dependency: transitive
    description:
      name: expandable_page_view
      sha256: "210dc6961cfc29f7ed42867824eb699c9a4b9b198a7c04b8bdc1c05844969dc6"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.17"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: ed5337a5660c506388a9f012be0288fb38b49020ce2b45fe1f8b8323fe429f99
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  file:
    dependency: transitive
    description:
      name: file
      sha256: "1b92bec4fc2a72f59a8e15af5f52cd441e4a7860b49499d69dfa817af20e925d"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.4"
  file_utils:
    dependency: transitive
    description:
      name: file_utils
      sha256: d1e64389a22649095c8405c9e177272caf05139255931c9ff30d53b5c9bcaa34
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  firebase_analytics:
    dependency: transitive
    description:
      name: firebase_analytics
      sha256: dbf1e7ab22cfb1f4a4adb103b46a26276b4edc593d4a78ef6fb942bafc92e035
      url: "https://pub.dev"
    source: hosted
    version: "10.10.7"
  firebase_analytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_analytics_platform_interface
      sha256: "3729b74f8cf1d974a27ba70332ecb55ff5ff560edc8164a6469f4a055b429c37"
      url: "https://pub.dev"
    source: hosted
    version: "3.10.8"
  firebase_analytics_web:
    dependency: transitive
    description:
      name: firebase_analytics_web
      sha256: "019cd7eee74254d33fbd2e29229367ce33063516bf6b3258a341d89e3b0f1655"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.7+7"
  firebase_core:
    dependency: transitive
    description:
      name: firebase_core
      sha256: "26de145bb9688a90962faec6f838247377b0b0d32cc0abecd9a4e43525fc856c"
      url: "https://pub.dev"
    source: hosted
    version: "2.32.0"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      sha256: "1003a5a03a61fc9a22ef49f37cbcb9e46c86313a7b2e7029b9390cf8c6fc32cb"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.0"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: "6643fe3dbd021e6ccfb751f7882b39df355708afbdeb4130fc50f9305a9d1a3d"
      url: "https://pub.dev"
    source: hosted
    version: "2.17.2"
  firebase_crashlytics:
    dependency: transitive
    description:
      name: firebase_crashlytics
      sha256: "9897c01efaa950d2f6da8317d12452749a74dc45f33b46390a14cfe28067f271"
      url: "https://pub.dev"
    source: hosted
    version: "3.5.7"
  firebase_crashlytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_crashlytics_platform_interface
      sha256: "16a71e08fbf6e00382816e1b13397898c29a54fa0ad969c2c2a3b82a704877f0"
      url: "https://pub.dev"
    source: hosted
    version: "3.6.35"
  firebase_performance:
    dependency: transitive
    description:
      name: firebase_performance
      sha256: dbcfc300755c4bb866988de20a491f0b53e1a0d14c375a2c31aa53ca82174c5b
      url: "https://pub.dev"
    source: hosted
    version: "0.9.4+7"
  firebase_performance_dio:
    dependency: transitive
    description:
      name: firebase_performance_dio
      sha256: aeec00853f9538feeac11edeceaefdd7115b099df9beee9b2fe53dc1236fdb33
      url: "https://pub.dev"
    source: hosted
    version: "0.7.1"
  firebase_performance_platform_interface:
    dependency: transitive
    description:
      name: firebase_performance_platform_interface
      sha256: "191c9945c2ea4359cb57dc086463b2a25b0f9d8d42f66a0be4c1a7133e26ebc8"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.4+35"
  firebase_performance_web:
    dependency: transitive
    description:
      name: firebase_performance_web
      sha256: "9f03a53f55697b206393366bf138e382cbd845d5021b5be6f7fc97b338da2cb5"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.6+7"
  firebase_remote_config:
    dependency: transitive
    description:
      name: firebase_remote_config
      sha256: "653bd94b68e2c4e89eca10db90576101f1024151f39f2d4e7c64ae6a90a5f9c5"
      url: "https://pub.dev"
    source: hosted
    version: "4.4.7"
  firebase_remote_config_platform_interface:
    dependency: transitive
    description:
      name: firebase_remote_config_platform_interface
      sha256: "24a2c445b15de3af7e4582ebceb2aa9a1e3731d0202cb3e7a1e03012440fa07d"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.35"
  firebase_remote_config_web:
    dependency: transitive
    description:
      name: firebase_remote_config_web
      sha256: "525aa3000fd27cd023841c802010a06515e564aab2f147aa964b35f54abbf449"
      url: "https://pub.dev"
    source: hosted
    version: "1.6.7"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: "25517a4deb0c03aa0f32fd12db525856438902d9c16536311e76cdc57b31d7d1"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_html:
    dependency: transitive
    description:
      name: flutter_html
      sha256: "02ad69e813ecfc0728a455e4bf892b9379983e050722b1dce00192ee2e41d1ee"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0-beta.2"
  flutter_inappwebview:
    dependency: transitive
    description:
      path: "."
      ref: HEAD
      resolved-ref: "3946536e294f3cd3afd022a6df41b128593d6019"
      url: "https://diegoassis1:<EMAIL>/somalabs/flutter_inappwebview_fork.git"
    source: git
    version: "5.8.0"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: a25a15ebbdfc33ab1cd26c63a6ee519df92338a9c10f122adda92938253bef04
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  flutter_localizations:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_native_splash:
    dependency: transitive
    description:
      name: flutter_native_splash
      sha256: af665ef80a213a9ed502845a3d7a61b9acca4100ee7e9f067a7440bc3acd6730
      url: "https://pub.dev"
    source: hosted
    version: "2.2.19"
  flutter_secure_storage:
    dependency: transitive
    description:
      name: flutter_secure_storage
      sha256: "22dbf16f23a4bcf9d35e51be1c84ad5bb6f627750565edd70dab70f3ff5fff8f"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.0"
  flutter_secure_storage_linux:
    dependency: transitive
    description:
      name: flutter_secure_storage_linux
      sha256: "3d5032e314774ee0e1a7d0a9f5e2793486f0dff2dd9ef5a23f4e3fb2a0ae6a9e"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  flutter_secure_storage_macos:
    dependency: transitive
    description:
      name: flutter_secure_storage_macos
      sha256: bd33935b4b628abd0b86c8ca20655c5b36275c3a3f5194769a7b3f37c905369c
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  flutter_secure_storage_platform_interface:
    dependency: transitive
    description:
      name: flutter_secure_storage_platform_interface
      sha256: "0d4d3a5dd4db28c96ae414d7ba3b8422fd735a8255642774803b2532c9a61d7e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  flutter_secure_storage_web:
    dependency: transitive
    description:
      name: flutter_secure_storage_web
      sha256: "30f84f102df9dcdaa2241866a958c2ec976902ebdaa8883fbfe525f1f2f3cf20"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  flutter_secure_storage_windows:
    dependency: transitive
    description:
      name: flutter_secure_storage_windows
      sha256: "38f9501c7cb6f38961ef0e1eacacee2b2d4715c63cc83fe56449c4d3d0b47255"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  flutter_slidable:
    dependency: transitive
    description:
      name: flutter_slidable
      sha256: "19ed4813003a6ff4e9c6bcce37e792a2a358919d7603b2b31ff200229191e44c"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  flutter_svg:
    dependency: transitive
    description:
      name: flutter_svg
      sha256: f991fdb1533c3caeee0cdc14b04f50f0c3916f0dbcbc05237ccbe4e3c6b93f3f
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      sha256: "408e3ca148b31c20282ad6f37ebfa6f4bdc8fede5b74bc2f08d9d92b55db3612"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  geocoding:
    dependency: transitive
    description:
      name: geocoding
      sha256: d580c801cba9386b4fac5047c4c785a4e19554f46be42f4f5e5b7deacd088a66
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  geocoding_android:
    dependency: transitive
    description:
      name: geocoding_android
      sha256: "1b13eca79b11c497c434678fed109c2be020b158cec7512c848c102bc7232603"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  geocoding_ios:
    dependency: transitive
    description:
      name: geocoding_ios
      sha256: "94ddba60387501bd1c11e18dca7c5a9e8c645d6e3da9c38b9762434941870c24"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  geocoding_platform_interface:
    dependency: transitive
    description:
      name: geocoding_platform_interface
      sha256: "8c2c8226e5c276594c2e18bfe88b19110ed770aeb7c1ab50ede570be8b92229b"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  geolocator:
    dependency: transitive
    description:
      name: geolocator
      sha256: "5c23f3613f50586c0bbb2b8f970240ae66b3bd992088cf60dd5ee2e6f7dde3a8"
      url: "https://pub.dev"
    source: hosted
    version: "9.0.2"
  geolocator_android:
    dependency: transitive
    description:
      name: geolocator_android
      sha256: "7aefc530db47d90d0580b552df3242440a10fe60814496a979aa67aa98b1fd47"
      url: "https://pub.dev"
    source: hosted
    version: "4.6.1"
  geolocator_apple:
    dependency: transitive
    description:
      name: geolocator_apple
      sha256: "1d938e2462cc5145c1402f89d49e70b60a2a51b89fb57414ced71417f1f479b1"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.3"
  geolocator_platform_interface:
    dependency: transitive
    description:
      name: geolocator_platform_interface
      sha256: b8cc1d3be0ca039a3f2174b0b026feab8af3610e220b8532e42cff8ec6658535
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  geolocator_web:
    dependency: transitive
    description:
      name: geolocator_web
      sha256: "59083f7e0871b78299918d92bf930a14377f711d2d1156c558cd5ebae6c20d58"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  geolocator_windows:
    dependency: transitive
    description:
      name: geolocator_windows
      sha256: "4f4218f122a6978d0ad655fa3541eea74c67417440b09f0657238810d5af6bdc"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3"
  get:
    dependency: "direct main"
    description:
      name: get
      sha256: "2ba20a47c8f1f233bed775ba2dd0d3ac97b4cf32fc17731b3dfc672b06b0e92a"
      url: "https://pub.dev"
    source: hosted
    version: "4.6.5"
  get_storage:
    dependency: transitive
    description:
      name: get_storage
      sha256: "39db1fffe779d0c22b3a744376e86febe4ade43bf65e06eab5af707dc84185a2"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: "0e7014b3b7d4dac1ca4d6114f82bf1782ee86745b9b42a92c9289c23d8a0ab63"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  globbing:
    dependency: transitive
    description:
      name: globbing
      sha256: "4f89cfaf6fa74c9c1740a96259da06bd45411ede56744e28017cc534a12b6e2d"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  html:
    dependency: transitive
    description:
      name: html
      sha256: "3a7812d5bcd2894edf53dfaf8cd640876cf6cef50a8f238745c8b8120ea74d3a"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.4"
  http:
    dependency: transitive
    description:
      name: http
      sha256: a2bbf9d017fcced29139daa8ed2bba4ece450ab222871df93ca9eec6f80c34ba
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      sha256: "97486f20f9c2f7be8f514851703d0119c3596d14ea63227af6f7a481ef2b2f8b"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  image:
    dependency: transitive
    description:
      name: image
      sha256: "4c68bfd5ae83e700b5204c1e74451e7bf3cf750e6843c6e158289cf56bda018e"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.7"
  in_app_review:
    dependency: transitive
    description:
      name: in_app_review
      sha256: "41ec6f30427ab09eb6ae1c85c4a2a624a145fc5d726f023de4d97170ec9e5466"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.8"
  in_app_review_platform_interface:
    dependency: transitive
    description:
      name: in_app_review_platform_interface
      sha256: fed2c755f2125caa9ae10495a3c163aa7fab5af3585a9c62ef4a6920c5b45f10
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  intl:
    dependency: "direct main"
    description:
      name: intl
      sha256: "3bc132a9dbce73a7e4a21a17d06e1878839ffbf975568bc875c60537824b0c4d"
      url: "https://pub.dev"
    source: hosted
    version: "0.18.1"
  io:
    dependency: transitive
    description:
      name: io
      sha256: "2ec25704aba361659e10e3e5f5d672068d332fc8ac516421d483a11e5cbd061e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  js:
    dependency: transitive
    description:
      name: js
      sha256: "5528c2f391ededb7775ec1daa69e65a2d61276f7552de2b5f7b8d34ee9fd4ab7"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.5"
  jwt_decoder:
    dependency: transitive
    description:
      name: jwt_decoder
      sha256: "54774aebf83f2923b99e6416b4ea915d47af3bde56884eb622de85feabbc559f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: "5e4a9cd06d447758280a8ac2405101e0e2094d2a1dbdd3756aec3fe7775ba593"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  list_counter:
    dependency: transitive
    description:
      name: list_counter
      sha256: c447ae3dfcd1c55f0152867090e67e219d42fe6d4f2807db4bbe8b8d69912237
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: "623a88c9594aa774443aa3eb2d41807a48486b5613e67599fb4c41c0ad47c340"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  lottie:
    dependency: transitive
    description:
      name: lottie
      sha256: "23522951540d20a57a60202ed7022e6376bed206a4eee1c347a91f58bd57eb9f"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  mask_text_input_formatter:
    dependency: transitive
    description:
      name: mask_text_input_formatter
      sha256: "8182cae94ff153e70071f86523d0d09112094e9de366a2e749e6aa9c865e70f2"
      url: "https://pub.dev"
    source: hosted
    version: "2.7.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: "1803e76e6653768d64ed8ff2e1e67bea3ad4b923eb5c56a295c3e634bad5960e"
      url: "https://pub.dev"
    source: hosted
    version: "0.12.16"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: "9528f2f296073ff54cb9fee677df673ace1218163c3bc7628093e7eed5203d41"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: a6e590c838b18133bb482a2745ad77c5bb7715fb0451209e1a7567d416678b8e
      url: "https://pub.dev"
    source: hosted
    version: "1.10.0"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: e4ff8e8564c03f255408decd16e7899da1733852a9110a58fe6d1b817684a63e
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  mocktail:
    dependency: transitive
    description:
      name: mocktail
      sha256: "80a996cd9a69284b3dc521ce185ffe9150cde69767c2d3a0720147d93c0cef53"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0"
  my_orders:
    dependency: "direct main"
    description:
      path: "../my_orders"
      relative: true
    source: path
    version: "0.0.1"
  node_preamble:
    dependency: transitive
    description:
      name: node_preamble
      sha256: "6e7eac89047ab8a8d26cf16127b5ed26de65209847630400f9aefd7cd5c730db"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: "1c5b77ccc91e4823a5af61ee74e6b972db1ef98c2ff5a18d3161c982a55448bd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  package_info_plus:
    dependency: transitive
    description:
      name: package_info_plus
      sha256: "7e76fad405b3e4016cd39d08f455a4eb5199723cf594cd1b8916d47140d93017"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: "9bc8ba46813a4cc42c66ab781470711781940780fd8beddd0c3da62506d3a6c6"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "8829d8a55c13fc0e37127c29fedf290c102f4e40ae94ada574091fe0ff96c917"
      url: "https://pub.dev"
    source: hosted
    version: "1.8.3"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: e3e67b1629e6f7e8100b367d3db6ba6af4b1f0bb80f64db18ef1fbabd2fa9ccf
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_provider:
    dependency: transitive
    description:
      name: path_provider
      sha256: a1aa8aaa2542a6bc57e381f132af822420216c80d4781f7aa085ca3229208aaa
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: e595b98692943b4881b219f0a9e3945118d3c16bd7e2813f98ec6e532d905f72
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "19314d595120f82aca0ba62787d58dde2cc6b5df7d2f0daf72489e38d1b57f2d"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "94b1e0dd80970c1ce43d5d4e050a9918fce4f4a775e6142424c30a29a363265c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: "8bc9f22eee8690981c22aa7fc602f5c85b497a6fb2ceb35ee5a5e5ed85ad8170"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  pay:
    dependency: transitive
    description:
      name: pay
      sha256: ad904db0e06848cade6990a3ce1e10e921ae48f7ee06447873e07b9688ac1fc5
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  pay_android:
    dependency: transitive
    description:
      name: pay_android
      sha256: aa46cd0ece1807d3fa293113fdb84afb5fc4b6ed60cf09a4886b753acb300859
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  pay_ios:
    dependency: transitive
    description:
      name: pay_ios
      sha256: "75ccb285d03f22b136c58ab8e8e0c4b614ee52a8b67e6ccfb680d4d8c04a70f6"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.11"
  pay_platform_interface:
    dependency: transitive
    description:
      name: pay_platform_interface
      sha256: "26a379e33c46508987c7afee8cde6f4aca5b5ab0afc697c27efbd33a9c2ea82a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  permission_handler:
    dependency: transitive
    description:
      name: permission_handler
      sha256: bc56bfe9d3f44c3c612d8d393bd9b174eb796d706759f9b495ac254e4294baa5
      url: "https://pub.dev"
    source: hosted
    version: "10.4.5"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      sha256: "59c6322171c29df93a22d150ad95f3aa19ed86542eaec409ab2691b8f35f9a47"
      url: "https://pub.dev"
    source: hosted
    version: "10.3.6"
  permission_handler_apple:
    dependency: transitive
    description:
      name: permission_handler_apple
      sha256: "99e220bce3f8877c78e4ace901082fb29fa1b4ebde529ad0932d8d664b34f3f5"
      url: "https://pub.dev"
    source: hosted
    version: "9.1.4"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: "6760eb5ef34589224771010805bea6054ad28453906936f843a8cc4d3a55c4a4"
      url: "https://pub.dev"
    source: hosted
    version: "3.12.0"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      sha256: cc074aace208760f1eee6aa4fae766b45d947df85bc831cde77009cdb4720098
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: "49392a45ced973e8d94a85fdb21293fbb40ba805fc49f2965101ae748a3683b4"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.0"
  pinch_zoom:
    dependency: transitive
    description:
      name: pinch_zoom
      sha256: ad12872281742726afaf03438d99a4572c584a612630768953beb6dfd6f9389a
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  pinput:
    dependency: transitive
    description:
      name: pinput
      sha256: "543da5bfdefd9e06914a12100f8c9156f84cef3efc14bca507c49e966c5b813b"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "0a279f0707af40c890e80b1e9df8bb761694c074ba7e1d4ab1bc4b728e200b59"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      sha256: "7c1e5f0d23c9016c5bbd8b1473d0d3fb3fc851b876046039509e18e0c7485f2c"
      url: "https://pub.dev"
    source: hosted
    version: "3.7.3"
  pool:
    dependency: transitive
    description:
      name: pool
      sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  pretty_dio_logger:
    dependency: transitive
    description:
      name: pretty_dio_logger
      sha256: "36f2101299786d567869493e2f5731de61ce130faa14679473b26905a92b6407"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "40d3ab1bbd474c4c2328c91e3a7df8c6dd629b79ece4c4bd04bee496a224fb0c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  qr:
    dependency: transitive
    description:
      name: qr
      sha256: "64957a3930367bf97cc211a5af99551d630f2f4625e38af10edd6b19131b64b3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  qr_flutter:
    dependency: transitive
    description:
      name: qr_flutter
      sha256: "5095f0fc6e3f71d08adef8feccc8cea4f12eec18a2e31c2e8d82cb6019f4b097"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  recaptcha_enterprise_flutter:
    dependency: transitive
    description:
      name: recaptcha_enterprise_flutter
      sha256: "1c10ae00abfee5c21c89c3e52c6a9b741ef972b581cd4c687ea14f6ac7bd43f8"
      url: "https://pub.dev"
    source: hosted
    version: "18.4.0"
  share_plus:
    dependency: transitive
    description:
      name: share_plus
      sha256: "3ef39599b00059db0990ca2e30fca0a29d8b37aae924d60063f8e0184cf20900"
      url: "https://pub.dev"
    source: hosted
    version: "7.2.2"
  share_plus_platform_interface:
    dependency: transitive
    description:
      name: share_plus_platform_interface
      sha256: df08bc3a07d01f5ea47b45d03ffcba1fa9cd5370fb44b3f38c70e42cced0f956
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  shelf:
    dependency: transitive
    description:
      name: shelf
      sha256: ad29c505aee705f41a4d8963641f91ac4cee3c8fad5947e033390a7bd8180fa4
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  shelf_packages_handler:
    dependency: transitive
    description:
      name: shelf_packages_handler
      sha256: "89f967eca29607c933ba9571d838be31d67f53f6e4ee15147d5dc2934fee1b1e"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  shelf_static:
    dependency: transitive
    description:
      name: shelf_static
      sha256: a41d3f53c4adf0f57480578c1d61d90342cd617de7fc8077b1304643c2d85c1e
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      sha256: "9ca081be41c60190ebcb4766b2486a7d50261db7bd0f5d9615f2d653637a84c1"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  showcaseview:
    dependency: transitive
    description:
      name: showcaseview
      sha256: dc62ce38820dead4a27ce39d9e6c98384be89c2f2b4da3255238a59b041c7ccd
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  smart_auth:
    dependency: transitive
    description:
      name: smart_auth
      sha256: a25229b38c02f733d0a4e98d941b42bed91a976cb589e934895e60ccfa674cf6
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  soma_analytics:
    dependency: "direct main"
    description:
      path: "../../../soma_analytics"
      relative: true
    source: path
    version: "0.0.1"
  soma_app_auth_module:
    dependency: transitive
    description:
      path: "../auth"
      relative: true
    source: path
    version: "0.0.1"
  soma_app_commons:
    dependency: "direct main"
    description:
      path: "../.."
      relative: true
    source: path
    version: "1.0.0+1"
  soma_app_omnilogic_module:
    dependency: transitive
    description:
      path: "../omnilogic"
      relative: true
    source: path
    version: "0.0.1"
  soma_app_promotions_module:
    dependency: "direct main"
    description:
      path: "../promotions"
      relative: true
    source: path
    version: "0.0.1"
  soma_core:
    dependency: "direct main"
    description:
      path: "../../../soma_core"
      relative: true
    source: path
    version: "0.0.1"
  soma_ui:
    dependency: "direct main"
    description:
      path: "../../../soma_ui"
      relative: true
    source: path
    version: "0.1.0"
  source_map_stack_trace:
    dependency: transitive
    description:
      name: source_map_stack_trace
      sha256: "84cf769ad83aa6bb61e0aa5a18e53aea683395f196a6f39c4c881fb90ed4f7ae"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  source_maps:
    dependency: transitive
    description:
      name: source_maps
      sha256: "708b3f6b97248e5781f493b765c3337db11c5d2c81c3094f10904bfa8004c703"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.12"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "53e943d4206a5e30df338fd4c6e7a077e02254531b138a15aec3bd143c1a8b3c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.0"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "73713990125a6d93122541237550ee3352a2d84baad52d375a4cad2eb9b7ce0b"
      url: "https://pub.dev"
    source: hosted
    version: "1.11.1"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: ba2aa5d8cc609d96bbb2899c28934f9e1af5cddbd60a827822ea467161eb54e7
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: ad47125e588cfd37a9a7f86c7d6356dde8dfe89d071d293f80ca9e9273a33871
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  super_tooltip:
    dependency: transitive
    description:
      name: super_tooltip
      sha256: "06f734caa2ddb2313813dac2c939f096222beece9c146ec7d71e07e6e869bef2"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  test:
    dependency: transitive
    description:
      name: test
      sha256: a1f7595805820fcc05e5c52e3a231aedd0b72972cb333e8c738a8b1239448b6f
      url: "https://pub.dev"
    source: hosted
    version: "1.24.9"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: "5c2f730018264d276c20e4f1503fd1308dfbbae39ec8ee63c5236311ac06954b"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.1"
  test_core:
    dependency: transitive
    description:
      name: test_core
      sha256: a757b14fc47507060a162cc2530d9a4a2f92f5100a952c7443b5cad5ef5b106a
      url: "https://pub.dev"
    source: hosted
    version: "0.5.9"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  universal_io:
    dependency: transitive
    description:
      name: universal_io
      sha256: "1722b2dcc462b4b2f3ee7d188dad008b6eb4c40bbd03a3de451d82c78bba9aad"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
  universal_platform:
    dependency: transitive
    description:
      name: universal_platform
      sha256: d315be0f6641898b280ffa34e2ddb14f3d12b1a37882557869646e0cc363d0cc
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0+1"
  upower:
    dependency: transitive
    description:
      name: upower
      sha256: cf042403154751180affa1d15614db7fa50234bc2373cd21c3db666c38543ebf
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  url_launcher:
    dependency: transitive
    description:
      name: url_launcher
      sha256: "21b704ce5fa560ea9f3b525b43601c678728ba46725bab9b01187b4831377ed3"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "17cd5e205ea615e2c6ea7a77323a11712dffa0720a8a90540db57a01347f9ad9"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.2"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: e43b677296fadce447e987a2f519dcf5f6d1e527dc35d01ffab4fff5b8a7063e
      url: "https://pub.dev"
    source: hosted
    version: "6.3.1"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: "9f2d390e096fdbe1e6e6256f97851e51afc2d9c423d3432f1d6a02a8a9a8b9fd"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: b7244901ea3cf489c5335bdacda07264a6e960b1c1b1a9f91e4bc371d9e68234
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "552f8a1e663569be95a8190206a38187b531910283c3e982193e4f2733f01029"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: fff0932192afeedf63cdd50ecbb1bc825d31aed259f02bb8dba0f3b729a5e88b
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "7754a1ad30ee896b265f8d14078b0513a4dba28d358eabb9d5f339886f4a1adc"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: "83d37c7ad7aaf9aa8e275490669535c8080377cfa7a7004c24dfac53afffaa90"
      url: "https://pub.dev"
    source: hosted
    version: "4.4.2"
  vector_graphics:
    dependency: transitive
    description:
      name: vector_graphics
      sha256: ea8d3fc7b2e0f35de38a7465063ecfcf03d8217f7962aa2a6717132cb5d43a79
      url: "https://pub.dev"
    source: hosted
    version: "1.1.5"
  vector_graphics_codec:
    dependency: transitive
    description:
      name: vector_graphics_codec
      sha256: a5eaa5d19e123ad4f61c3718ca1ed921c4e6254238d9145f82aa214955d9aced
      url: "https://pub.dev"
    source: hosted
    version: "1.1.5"
  vector_graphics_compiler:
    dependency: transitive
    description:
      name: vector_graphics_compiler
      sha256: "15edc42f7eaa478ce854eaf1fbb9062a899c0e4e56e775dd73b7f4709c97c4ca"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.5"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  video_player:
    dependency: transitive
    description:
      name: video_player
      sha256: "74b86e63529cf5885130c639d74cd2f9232e7c8a66cbecbddd1dcb9dbd060d1e"
      url: "https://pub.dev"
    source: hosted
    version: "2.7.2"
  video_player_android:
    dependency: transitive
    description:
      name: video_player_android
      sha256: "3fe89ab07fdbce786e7eb25b58532d6eaf189ceddc091cb66cba712f8d9e8e55"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.10"
  video_player_avfoundation:
    dependency: transitive
    description:
      name: video_player_avfoundation
      sha256: bf1a1322bf68bccd349982ba1f5a41314a3880861fb9a93d25d6d0a2345845f0
      url: "https://pub.dev"
    source: hosted
    version: "2.4.11"
  video_player_platform_interface:
    dependency: transitive
    description:
      name: video_player_platform_interface
      sha256: be72301bf2c0150ab35a8c34d66e5a99de525f6de1e8d27c0672b836fe48f73a
      url: "https://pub.dev"
    source: hosted
    version: "6.2.1"
  video_player_web:
    dependency: transitive
    description:
      name: video_player_web
      sha256: "9c34a243785feca23148bfcd772dbb803d63c9304488177ec4f3f4463802fcb7"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.17"
  visibility_detector:
    dependency: "direct main"
    description:
      name: visibility_detector
      sha256: dd5cc11e13494f432d15939c3aa8ae76844c42b723398643ce9addb88a5ed420
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0+2"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: b3d56ff4341b8f182b96aceb2fa20e3dcb336b9f867bc0eafc0de10f1048e957
      url: "https://pub.dev"
    source: hosted
    version: "13.0.0"
  wakelock_plus:
    dependency: transitive
    description:
      name: wakelock_plus
      sha256: f268ca2116db22e57577fb99d52515a24bdc1d570f12ac18bb762361d43b043d
      url: "https://pub.dev"
    source: hosted
    version: "1.1.4"
  wakelock_plus_platform_interface:
    dependency: transitive
    description:
      name: wakelock_plus_platform_interface
      sha256: "40fabed5da06caff0796dc638e1f07ee395fb18801fbff3255a2372db2d80385"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: "6a7f46926b01ce81bfc339da6a7f20afbe7733eff9846f6d6a5466aa4c6667c0"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  web:
    dependency: "direct overridden"
    description:
      name: web
      sha256: "97da13628db363c635202ad97068d47c5b8aa555808e7a9411963c533b449b27"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.1"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: d88238e5eac9a42bb43ca4e721edba3c08c6354d4a53063afaa568516217621b
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  webkit_inspection_protocol:
    dependency: transitive
    description:
      name: webkit_inspection_protocol
      sha256: "67d3a8b6c79e1987d19d848b0892e582dbb0c66c57cc1fef58a177dd2aa2823d"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  webview_flutter:
    dependency: transitive
    description:
      name: webview_flutter
      sha256: "6869c8786d179f929144b4a1f86e09ac0eddfe475984951ea6c634774c16b522"
      url: "https://pub.dev"
    source: hosted
    version: "4.8.0"
  webview_flutter_android:
    dependency: transitive
    description:
      name: webview_flutter_android
      sha256: "0d21cfc3bfdd2e30ab2ebeced66512b91134b39e72e97b43db2d47dda1c4e53a"
      url: "https://pub.dev"
    source: hosted
    version: "3.16.3"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      sha256: d937581d6e558908d7ae3dc1989c4f87b786891ab47bb9df7de548a151779d8d
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  webview_flutter_wkwebview:
    dependency: transitive
    description:
      name: webview_flutter_wkwebview
      sha256: "9c62cc46fa4f2d41e10ab81014c1de470a6c6f26051a2de32111b2ee55287feb"
      url: "https://pub.dev"
    source: hosted
    version: "3.14.0"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: f2add6fa510d3ae152903412227bda57d0d5a8da61d2c39c1fb022c9429a41c0
      url: "https://pub.dev"
    source: hosted
    version: "5.0.6"
  win32_registry:
    dependency: transitive
    description:
      name: win32_registry
      sha256: e4506d60b7244251bc59df15656a3093501c37fb5af02105a944d73eb95be4c9
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "589ada45ba9e39405c198fe34eb0f607cddb2108527e658136120892beac46d2"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: "979ee37d622dec6365e2efa4d906c37470995871fe9ae080d967e192d88286b5"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.2"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: "75769501ea3489fca56601ff33454fe45507ea3bfb014161abc3b43ae25989d5"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
sdks:
  dart: ">=3.2.3 <4.0.0"
  flutter: ">=3.16.6"
