name: farm_app_flutter
description: A new Flutter project.

publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 2.65.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2
  soma_ui:
    path: ../soma_ui/
  soma_core:
    path: ../soma_core/
  soma_analytics_insider:
    path: ../soma_analytics/soma_analytics_insider
  soma_app_commons:
    path: ../soma_app_commons/
  soma_app_omnilogic_module:
    path: ../soma_app_commons/modules/omnilogic
  product_review:
    path: ../soma_app_commons/modules/product_review
  soma_app_order_tracking_module:
    path: ../soma_app_commons/modules/order_tracking
  soma_app_promotions_module:
    path: ../soma_app_commons/modules/promotions
  soma_app_product_details_module:
    path: ../soma_app_commons/modules/product_details
  soma_app_checking_account:
    path: ../soma_app_commons/modules/checking_account
  soma_app_payment:
    path: ../soma_app_commons/modules/payment
  soma_app_appsflyer_module:
    path: ../soma_app_commons/modules/appsflyer/
  soma_app_notification_center_module:
    path: ../soma_app_commons/modules/notification_center
  soma_app_facebook_module:
    path: ../soma_app_commons/modules/facebook/
  soma_app_dito_module:
    path: ../soma_app_commons/modules/dito/
  soma_app_clocks_module:
    path: ../soma_app_commons/modules/clocks
  futuriza:
    path: ../soma_app_commons/modules/futuriza
  deeplink:
    path: ../soma_app_commons/modules/deeplink

  get_storage: ^2.0.3
  app_tracking_transparency: ^2.0.2+4
  uni_links: ^0.5.1
  firebase_crashlytics: 3.5.7
  flutter_insider: 3.18.1+nh
  auto_size_text: ^3.0.0
  transparent_pointer: ^1.0.0
  background_fetch: ^1.2.2
  device_info_plus: ^9.0.3
  futuriza_flutter: ^0.1.7
  firebase_core: ^2.9.0
  firebase_messaging: ^14.4.0

dependency_overrides:
  web: ^0.5.1
  http: 1.1.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

  flutter_lints: ^2.0.1
  flutter_native_splash:

flutter:
  fonts:
    - family: FarmIcons
      fonts:
        - asset: packages/soma_ui/fonts/farm/FarmIcons.ttf

    - family: VolumeIcons
      fonts:
        - asset: packages/soma_ui/fonts/farm/VolumeIcons.ttf

    - family: Montserrat
      fonts:
        - asset: packages/soma_ui/fonts/farm/Montserrat-Light.ttf
        - asset: packages/soma_ui/fonts/farm/Montserrat-Regular.ttf
        - asset: packages/soma_ui/fonts/farm/Montserrat-SemiBold.ttf
        - asset: packages/soma_ui/fonts/farm/Montserrat-Bold.ttf

  uses-material-design: true

  assets:
    - assets/images/
    - assets/lottie/
    - assets/gifs/

flutter_native_splash:
  color: "#000000"

  android_12:
    icon_background_color: "#ffffff"
    icon_background_color_dark: "#ffffff"
