import 'package:farm_app_flutter/widgets/transition_screen.dart';
import 'package:get/get.dart';
import 'package:soma_ui/soma_ui.dart';
import 'package:soma_app_commons/soma_app_commons.dart';

import '../../screens/main.dart';
import '../../screens/lp_custom/lp_custom.dart';
import '../../screens/cashback/cashback.dart';
import '../../screens/wishlist/wishlist_folder_products.dart';
import '/screens/splash/splash.dart';

var routesConfig = RoutesConfig();
List<GetPage> getPages(AppRoutes routes) {
  return routesConfig.createPages({
    ...routesConfig.createDefaultRoutes(routes)
      ..remove(routes.orders)
      ..remove(routes.trackOrder),
    routes.splash: () => const Splash(),
    routes.home: () => const Main(),
    routes.tapume: () => const SmTapume(),
    routes.cashbackWallet!: () => const CashbackWallet(),
    routes.cashbackExtract!: () => const CashbackExtract(),
    routes.cashbackStatus!: () => const CashbackStatus(),
    routes.cashbackDetails!: () => const CashbackDetails(),
    routes.cashbackFaq!: () => const CashbackFaq(),
    routes.cashbackFaqDetails!: () => const CashbackFaqDetails(),
    routes.wishlistFolderProducts!: () => const WishlistFolderProducts(),
    routes.lpCustom!: () => const LpCustom(),
    routes.suggestions: () => const SmSuggetionsPage(),
    routes.transitionEtc: () => const TransitionScreen()
  }, routes);
}
