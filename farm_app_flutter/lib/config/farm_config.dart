import 'package:soma_core/config/models/checking_account_config.dart';
import 'package:soma_core/config/models/credit_voucher_config.dart';
import 'package:soma_core/soma_core.dart';

Config coreConfigBuilder() {
  final farmConfig = Config(
    somaBusConfig: productionPosSoma,
    prismicConfig: productionPrismicConfig,
    cmsConfig: productionCmsConfig,
    store: Store(
      defaultSalesChannel: 9,
      storeName: "lojafarm",
      storeUrl: 'https://www.farmrio.com.br',
      utmiCampaign: UTMICampaign(addCode: "codigodavendedora", removeCode: ""),
    ),
    masterData: MasterData(salesPersonTable: 'VD'),
    appUrls: const AppUrls(
      privacyWarning:
          'https://www.farmrio.com.br/institucional/aviso-de-privacidade',
      faq: 'https://www.farmrio.com.br/fala-farm',
      termsAndConditions: 'https://www.farmrio.com.br/institucional/politicas',
      devolutionRouteUrlTemplate:
          'https://farmrio.troque.app.br?order={orderId}&client={userEmail}',
      oneTrustDeleteAccountUrl:
          "https://azzas2154-privacy.my.onetrust.com/webform/************************************/c33aabbc-a65a-4909-82fb-569370c6b193",
    ),
    appFeaturesConfig: AppFeaturesConfig(
      selectGift: const SelectGiftConfig(isEnabled: true, showSkuName: true),
      productsTags: true,
      hideFavoriteButtonGridSizeThree: true,
      giftCardProductConfig: GiftCardProductConfig(
        idGiftCardProduct: '1394',
        isCollection: true,
        isEnabled: true,
        isEGiftCard: true,
      ),
      giftCardConfig: const GiftCardConfig(isEnabled: false),
      updateProductPDP: false,
      giftWrapConfig: const GiftWrapConfig(),
      storePickupConfig: const StorePickupConfig(),
      showProducRefCodeBelowTitlePDP: true,
      cashBackConfig: CashBackConfig(provider: 'LemoneyOCB', isEnabled: false),
      dressingRoom: true,
      wishlistV2: true,
      trackingUrl: true,
      statusPhone2Business: true,
      fastGetOrders: false,
      storeUpdateApp: false,
      creditVoucherConfig: CreditVoucherConfig(provider: '', isEnabled: false),
      isEnablePartnerSellerTag: true,
      checkingAccountConfig: const CheckingAccountConfig(isEnabled: true),
      validSellerCodeWithoutName: true,
      showSkuPdc: true,
      filterByUserCluster: true,
      showTotalAndMaxInstallmentsCheckoutResume: true,
      orderConfirmationConfig: OrderConfirmationConfig(
        suggestedProductsSearch: Search(
          filterCategoryOrCluster: '/productclusterids/689',
        ),
      ),
      enablePixInstallments: true,
      useControllerShippingOptions: true,
      enableAutoSelectSize: true,
      showInstallmentsBelowRefCode: false,
      hideSimilarProduct: true,
      showAttentionDeliveryCard: true,
    ),
    buttonsRedirects: ButtonsRedirects(
      genericError: Search(
        filterCategoryOrCluster: '/productclusterids/689',
      ),
      emptyBag: Search(
        filterCategoryOrCluster: '/productclusterids/689',
      ),
    ),
    tabItems: TabItemConfig(tabItems: {
      'home': TabItem(index: 0, name: 'inicio'),
      'news': TabItem(index: 1, name: 'novidades'),
      'explore': TabItem(index: 2, name: 'explore'),
      'profile': TabItem(index: 3, name: 'perfil'),
      'bag': TabItem(index: 4, name: 'mochila'),
    }),
    intelligentSearchConfig: IntelligentSearchConfig(
      sellername: '',
      filters: IntelligentSearchConfig.defaultFiltersConfigFor([
        FilterType.categories,
        FilterType.colors,
        FilterType.sizes,
        FilterType.products,
        FilterType.price,
      ]),
    ),
    strapiConfig: const StrapiConfig(
      accessToken:
          'd92516de2defb67072499b153ce3d502fbe19037358615b8777d27fe33f62fdf53ab3968de34b1a8378f189e80079d6cbac2f1dee293a1561bd531d522139b01ee03dae81235385e3582716296f90444650f1957021e0207b9074856c0ffc2c1da9b9363468cb122954d52f84595bd919dd583ebf04b3416bb74dd98a55402cb',
    ),
    liveStream: const LiveStreamConfig(
      url:
          'https://backend-production-user-jysqyijvoa-uc.a.run.app/stream/aovivo',
      categoryOrClusterRegex: '1138',
    ),
    orderFormConfig: const OrderFormConfig(
      maxSameItemQuantity: 100,
    ),
    whatsapp: Whatsapp(
      phone: '+552137337701',
    ),
    recaptchaConfig: const RecaptchaConfig(
      androidKey: '6LfqfuQnAAAAAJxoi1llgghJ4jYwJJmK0WoAhoL7',
      iosKey: '6LcyiOQnAAAAAOvt9VA4SA_JnC79L_mcR327ebyP',
    ),
    futurizaConfig:
        const FuturizaConfig(floatingButtonType: FloatingButtonType.header),
    maxDiscountConfig: const MaxDiscountConfig(maxDiscount: 80),
    contentDevolutionConfig: const ContentDevolutionConfig(
      showContent: false,
      showBottomSheet: true,
    ),
  );

  return farmConfig;
}

const productionPrismicConfig = PrismicConfig(
  baseUrl: 'https://somabus-kong.somalabs.com.br/api/cms-cache/farmappflutter/',
  accessToken:
      'MC5ZWk84NkJFQUFDQUFOTUR0.VDjvv71H77-9Rh1t77-9Xu-_vXEm77-977-977-9NAHvv71S77-977-977-9Ku-_ve-_ve-_vURR77-977-977-9',
  forceCache: true,
);

const stagingPrismicConfig = PrismicConfig(
  baseUrl:
      'https://somabus-kong.somalabs.com.br/api/cms-cache/farmappflutterhomolog/',
  accessToken:
      'MC5ZMlBsenhRQUFMT3FuV1B3.77-9NwJ8Re-_vUnvv73vv73vv70wIO-_vVTvv73vv70b77-977-9UO-_ve-_ve-_vXTvv73vv73vv70EOFJ-fA',
);

const productionCmsConfig = CmsConfig(
  baseUrl: 'https://cms-farm.somalabs.com.br/api/',
  accessToken:
      'a6f29feed1d36c7a080b1b227f747437e1ad4b1566693546017e6df8ad0e9e0d1f1b86cee99a69c3006b2ff1234a527b7efff6f3e88ece93b54940a0582a22aaa383e231eff215f77da2f96b289abbe1043ec5c01fe754ce013e7951325772871a79a4e008ff3bfb8c23c50c4a663671b8f3728a3f1f5e19aadcdc60645459c7',
);

const stagingCmsConfig = CmsConfig(
  baseUrl: 'https://cms-farm-homolog.somalabs.com.br/api/',
  accessToken:
      'af40984d61bb6c6af43bbc7c62384c25751530a8cecc151afaee1012907f0e033e7616832a4a0dda227321be22da36e53475c22a3a6dfcaaeaee798322187f2b27472c6050481b99266839bb94c85dbd30b2b99f921dd1a54715dc0953db6038cee5554a12569ec3ef07e2a3b37dc77b7599054f3e13c457ee3a8fb6ba15fc0d',
);

const localCmsConfig = CmsConfig(
  baseUrl: 'http://localhost:1337/api/',
  accessToken: '',
);

final productionPosSoma = SomaBusConfig(
  posSoma: 'Zjk2YTliMTItMThiYy00ODRjLWI3ZGYtMjdhMDk5MmI4Yzc3',
);

final homologPosSoma = SomaBusConfig(
  posSoma: 'MGE0YTUzNjUtMTY5MS00NjVkLTgyMmMtMmUwNjQzYzBiY2Y5',
);
