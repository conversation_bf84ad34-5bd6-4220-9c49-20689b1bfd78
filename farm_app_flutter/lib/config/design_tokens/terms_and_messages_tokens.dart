import 'package:soma_ui/soma_ui.dart';

const farmTermsAndMessagesTokens = DTTermsAndMessages(
  ordersScreenTitle: 'pedidos e devoluções',
  bag: 'mochila',
  wishlist: 'desejos',
  menu: 'explorar',
  buyButton: 'comprar',
  addToBag: 'incluir na mochila',
  removeFromBag: 'produto removido da mochila',
  bagProductDoesntAcceptPaymentMethod:
      'ops, um dos produtos na sua mochila não aceita essa forma de pagamento',
  wishlistRouteTitle: 'ver meus desejos salvos',
  emptyBagTitle: 'sua mochila está vazia',
  emptyBagMessage:
      '''confira os produtos salvos na sua lista de desejos ou explore nossas novidades!''',
  emptyBagButton: 'ver novidades',
  emptyBagWishlistTitle: 'seus desejos salvos',
  emptyBagWishlistMessage: 'Leve agora o que você deixou pra depois',
  sadSmile: ':(',
  happySmile: ':)',
  otherLookProducts: 'gostou da combinação? leve o look',
  searchLabelText: 'o que você procura?',
  genericErrorTitle: 'vamos tentar de novo?',
  genericErrorSubtitle: 'tivemos um probleminha para carregar essa página.'
      '\n\nretorne à página anterior ou confira os nossos novos desejos aqui',
  genericErrorButton: 'explorar novidades',
  productShippingTitle:
      'calcule o frete ou verifique em qual loja retirar grátis',
  productExchangeOrReturn: 'troca e devolução',
  productMaterials: 'feita de que',
  productTitleDescription: 'Sobre a peça',
  productPaymentWaysDescription: '',
  productExchangeOrReturnDescription:
      'a peça não ficou como você esperava? vem cá, a gente te ajuda!'
      '\n\nos produtos FARM Rio comprados no nosso site ou app podem ser trocados nas lojas físicas com benefício! funciona assim: se o valor da sua nova compra for maior do que o valor da peça levada para troca, você ganha 10%OFF na diferença. ah, essa vantagem é exclusiva nas lojas. consulte a política do benefício com a vendedora'
      '\n\no prazo das trocas na loja é de até 30 (trinta) dias corridos, contados a partir do recebimento do pedido. vale lembrar: a apresentação da nota fiscal é obrigatória pra que a gente possa dar seguimento na troca!'
      '\n\ncaso prefira receber créditos pela troca para realizar uma compra futura no site ou app, você pode solicitar a devolução dos produtos através dos nossos canais digitais. após o login, é só acessar a área “meus pedidos” e clicar no botão “solicitar devolução”. após a solicitação, você receberá um email automático dos correios com o código de postagem - caso não receba o email, esse código também fica disponível no seu perfil do site e do app, com data e validade.'
      '\n\nno site ou app, assim que a sua devolução for aprovada, vamos adicionar os créditos na sua carteira FARM. para visualizar, acesse a área “minha carteira” dentro do seu perfil. na sua compra futura, será possível usar os créditos na etapa de pagamento, na finalização do pedido!'
      '\n\nagora, se você quer receber o estorno do pedido através do site ou app, vá na área “meu pedidos”, clique em “solicitar devolução” e opte pelo estorno. depois de uma revisão, a gente vai te enviar um email com as informações do ressarcimento, é só aguardar!'
      '\n\naqui, as devoluções são permitidas para compras com status de entregues e dentro do prazo de até 07 (sete) dias corridos após o recebimento, tá bem?! caso você opte em receber o vale-troca como crédito, esse prazo se estende para até 14 (quatorze) dias corridos. fora desses prazos, nosso sistema desabilita automaticamente a permissão.'
      '\n\nse ainda estiver com dúvidas, é só consultar as nossas políticas!',
  productPickupOneDay: 'retire em 1 dia útil*',
  productPickupOneDayDescription:
      'para retirar sua compra em loja, informe o seu CEP e veja quais '
      'lojas próximas estão com o produto disponível para a retirada. '
      '\n\ncoloque seu desejo na mochila, confira seu CEP e selecione a '
      'opção de retirada em loja. escolha uma loja entre as opções apresentadas, '
      'informe quem vai ser responsável pela retirada e feche a compra. '
      '\n\n*quando o pedido estiver pronto, você vai receber um email informando '
      'que o produto está disponível para retirada. só é possível retirar o pedido após '
      'o email de confirmação! o prazo para buscar a compra é de até 7 dias, após a confirmação. '
      'passado esse prazo, a compra é cancelada automaticamente.',
  productPickupTag: 'também disponível para retirada em loja',
  closePurchase: 'fechar compra',
  giftWrap: 'embalagem de presente',
  bagDeliverySubtitle: 'receba em casa ou retire na loja',
  emailCheckTitle: 'digite seu email',
  bagCouponCodVendedorTitle: 'quer usar um código de vendedora e/ou cupom?',
  productLowStockLabel: 'pouco estoque!',
  bagCouponCodVendedorInputPlaceholder: 'código ou cupom',
  loginEmailInputPlaceholder: 'E-mail',
  sizesTableBottomSheetTitle: 'medidas da peça',
  sizesTableBottomSheetSubtitle:
      'selecione um ou mais tamanhos pra saber quais as medidas do {nameOfProduct}. '
      '\natenção: as medidas mostradas se referem às medidas da peça',
  profileTitle: 'já criou sua conta?',
  profileSubtitle:
      'acompanhe seus pedidos, favorite seus produtos preferidos, gerencie seus cartões e muito mais!',
  profileHelpOptionsTitle: 'ficou com dúvida?',
  contactUsThroughWhatsapp: 'falar pelo whatsapp',
  accessFaq: 'acessar o nosso FAQ',
  lastProductMessage: 'resta 1!',
  missingSize: 'meu tamanho acabou',
  emptyWishlistTitle: 'nenhum desejo salvo',
  emptyWishlistMessage:
      'adicione seus itens preferidos para ter acesso a sua lista de desejos',
  failedPaymentMessage:
      'não foi possível finalizar sua compra. Por favor, escolha outra forma de pagamento ou tente novamente.',
  failedPaymentPageTitle:
      'ops, ocorreu um erro com a forma de pagamento utilizada',
  wishlistSoldOutSizeMessage:
      'Ops, seu desejo no tamanho {size} acabou {smile}',
  wishlistSavedSuccessfuly: 'produto salvo em desejos!',
  wishlistRemovedSuccessfuly: 'produto removido dos desejos',
  wishlistErrorOnLoad: 'não foi possível carregar seus desejos',
  warnMeSubtitle:
      'fique de olho no seu email, caso o seu desejo fique disponível novamente mandaremos o aviso por lá, ok?',
  giftCardRules: [
    'o cartão presente virtual é um produto 100% digital, exclusivo para utilização no site da FARM (www.farmrio.com.br);',
    'o valor pago pelo seu cartão presente será totalmente revertido em créditos para presentear;',
    'ao adquirir seu cartão presente, você vai receber um e-mail com as informações de como utilizá-lo;',
    'é de responsabilidade do comprador a utilização do código único do cartão presente, que poderá ser atrelado a apenas um CPF;',
    'ao informar o e-mail da pessoa presenteada, não será possível realizar a troca de titularidade. caso não informe o e-mail o cartão presente será enviado para o e-mail da pessoa que estiver comprando o cartão;',
    'seu cartão presente vale por um ano no site da FARM;',
    'o cartão presente pode ser utilizado de forma parcial, ou seja, não é necessário utilizá-lo todo em uma compra;',
    'ao utilizar o cartão presente, se o total da compra tiver um valor superior ao do cartão presente, o valor poderá ser complementado através das formas de pagamento débito e crédito;',
    'o cartão presente pode ser adquirido no site através das formas de pagamento disponíveis no mesmo;',
    'o cartão presente só poderá ser cancelado pelo comprador, com a condição de que o saldo ainda não tenha sido utilizado;',
    'em caso de devolução do produto adquirido com o vale presente, o estorno será realizado de acordo com a forma de pagamento usada na compra. O valor utilizado através do vale retorna para o mesmo e o utilizado através do cartão de crédito ou débito é estornado normalmente;',
    'o vale presente é um produto virtual. ele não será entregue no endereço cadastrado no fechamento da compra;',
    'o tempo de recebimento do e-mail com o código do vale presente para ser utilizado no site pode demorar até 30min para chegar na sua caixa de e-mail.'
  ],
  giftCardEmailTitle: 'pra quem vai o mimo?',
  giftCardEmailSubtitle:
      'digite o e-mail da pessoa especial que você vai presentear. vamos enviar o vale diretamente pra ela! a surpresa é certa e o presente é certeiro :)',
  giftCardMessageTitle: 'vai! abre o coração!',
  giftCardMessageSubtitle:
      'junto com o vale, você também pode enviar uma mensagem personalizada! é só escrever aqui embaixo.',
  trackingOrdersTitle: 'acompanhar pedido',
  cashbackInfo: [
    'esse é o valor disponível de cashback pra usar nessa compra. o cashback máximo aplicável por novo pedido é de 30% do valor total, sem o frete.',
    'lembra que garrafinhas, sacolinhas e vale-presente não podem ser comprados com cashback. em caso de dúvidas, consulte nosso faq completo no site.'
  ],
  cashbackInfoEstimatedValue: [
    'este é o cashback estimado ao comprar esse produto. mas fica de olho: podem ocorrer mudanças, de acordo com os cupons, vales e produtos adicionados à mochila. ',
    'o valor exato fica disponível na etapa de revisão do pedido! '
  ],
  cashbackToMeetDetails:
      'todas as suas compras no nosso app têm 10% do dinheiro de volta. e aqui, na sua carteira digital, você pode conferir seus saldos, prazos e transações do cashback.',
  cashbackToMeetTitle: 'sua nova carteira FARM!',
  emptySearchTitle: 'ops, sua busca não foi encontrada',
  emptySearchSubtitle:
      'você pode tentar utilizar um outro termo ou navegar através das categorias',
  emptySearchButtonTitle: 'pesquisar com outro termo',
  cashbackStatusActive: [
    'continue comprando e gerando cashback para manter a sua carteira ativa.',
    'lembra que cada cashback tem o prazo de 30 dias e que, caso não realize compras, o seu saldo será expirado e a sua carteira pode ficar inativa'
  ],
  cashbackStatusBlocked: [
    'a carteira de cashback não está disponível para o seu login. entre em contato com o atendimento para entender a situação.'
  ],
  cashbackStatusInactive: [
    'parece que você ficou muito tempo sem comprar ou que seu saldo de cashback ultrapassou o prazo de 30 dias. deixe sua carteira ativa outra vez fazendo novos pedidos e gerando cashbacks.'
  ],
  emptyBottomSheetSizeTableTitle: 'tabela de medidas indisponível',
  emptyBottomSheetSizeTableSubtitle:
      'a tabela de medidas para esse produto está indisponível. pedimos desculpas por isso.',
  emptyBottomSheetSizeTableButtonText: 'voltar',
  profileWishlistItemTitle: 'meus desejos',
  trackSingleOrderButtonTitle: 'acompanhar pedido',
  trackMultipleOrdersButtonTitle: 'acompanhar pedidos',
  orderSuggestedProductsTitle: 'tá na vitrine',
  orderSuggestedProductsSubtitle: 'para combinar com o seu look novo',
  emailInputPlaceholder: "<EMAIL>",
  changeAddressLinkTextBag: 'alterar endereço',
  addressLinkTextBag: 'calcular',
  bottomSheetGiftCardDeleteButtonTitle: 'excluir benefício',
  paymentCoverGiftCard:
      'o seu vale cobriu o valor total da compra. não será necessário selecionar uma forma de pagamento.',
  productOutOfStock: 'avise-me',
  warnMeSuccessFeedBackTitle: 'pronto, pode deixar que a gente te avisa!',
  warnMeText: 'deseja receber um aviso caso ele volte pro nosso estoque?',
  partnerSellerTagText: 'vendido por parceiro FARM',
  partnerSellerBottomSheetTitle: 'políticas parcerias',
  partnerSellerBottomSheetSubtitle:
      'em produtos vendidos por parceiro, as condições de entrega e pagamento podem variar',
  partnerSellerBottomSheetDelivery:
      'o frete grátis a partir R\$399 com código de vendedor* é válido apenas para produtos vendidos '
      'e entregues pela FARM Rio, não sendo elegível para itens vendidos ou entregues por parceiros. '
      'o custo de envio pode sofrer alterações de acordo com as políticas de entrega. '
      'verifique todos os detalhes direto na sua mochila antes de finalizar a compra.',
  partnerSellerBottomSheetExchangeAndReturn:
      'os produtos dos parceiros FARM não podem ser trocados em loja, nas compras feitas pelo '
      'site você pode solicitar apenas a devolução via postagem dos correios, com o prazo de até 07 dias '
      'corridos após o recebimento.',
  partnerSellerBottomSheetPromotions:
      'os produtos de parceira nem sempre participam das promoções vigentes no site. fique de olho nas '
      'regras de cada promoção para descobrir se o item que você deseja é aplicável à promoção.',
  partnerSellerBottomSheetPayment: 'só é aceito pix e cartão de crédito '
      '(parcelamento). não são aceitos '
      'pagamento com vale troca e GIFTCARD da FARM.',
  partnerSellerBottomSheetFooterText:
      'em caso de dúvidas com os pedidos de parcerias, você pode entrar em contato com os nossos '
      'canais de atendimento. boas compras!',
  partnerSellerDefaultDescription:
      'vendido por parceiro, condições de entrega e pagamento podem variar',
  checkingAccountTitle: 'minha carteira',
  checkingAccountSubtitle: 'meus créditos',
  checkingAccountTransactionsTitle: 'extrato',
  checkingAccountNoOutflowsMessage:
      'você ainda não utilizou nenhum valor do seu crédito.',
  checkingAccountFirstAccessBottomSheetDescription:
      'todos os créditos que você tiver na FARM ficam aqui na sua carteira: de valores gerados por devoluções a bônus recebidos. visualize todas as movimentações e prazos de uso por aqui. um jeito mais fácil de acompanhar e resgatar seus créditos!',
  checkingAccountFirstAccessBottomSheetButton: 'entendi',
  checkingAccountCheckoutFeedbackTitle: 'você possui um crédito de',
  checkingAccountCheckoutFeedbackTooltipText:
      'esse valor é referente a créditos gerados por devoluções que você realizou ou a bônus recebidos, válidos para compras online.',
  checkingAccountOrderConfirmationFeedbackSubtitle:
      'aproveite para usar nas\npróximas compras!',
  checkingAccountSelectPaymentOption: 'quero utilizar meus créditos',
  checkingAccountCompletePaymentBottomSheetButton:
      'completar forma de pagamento',
  checkingAccountCompletePaymentBottomSheetExplanation:
      'o valor de crédito escolhido é inferior ao valor total do pedido. caso não haja mais crédito na carteira, complete o valor total com uma outra forma de pagamento, como cartão de crédito/débito ou pix.',
  checkingAccountCheckoutResumeCode: 'meus créditos utilizados',
  checkingAccountPaymentMethodsTitle: 'pagamento com meus créditos',
  checkingAccountPaymentMethodsSubtitle: 'meus créditos',
  checkingAccountCheckouStepSubtitle: 'meus créditos utilizados',
  checkingAccountEmptyAccountContent:
      'você não possui nenhum crédito disponível para uso.',
  checkingAccountEmptyAccountButtonText: 'saiba mais',
  selectGiftTitle: 'você ganhou um brinde!',
  selectGiftSubtitle: 'escolha aqui qual você mais gostou:',
  addToBagOtherLookProductCard: 'selecionar tamanho',
  otherLookProductsSubtitle:
      'esses são os produtos para compor a sua produção. escolha o tamanho e compre junto',
  cpfCheckAuthStepPageLayoutTitle: 'seja bem vindo (a)',
  cpfCheckAuthStepPageLayoutTextFieldLabelTextInitalValue: '000.000.000-00',
  cpfCheckAuthStepPageLayoutTextFieldLabelTextOnFocus: 'cpf',
  cpfCheckAuthStepPageLayoutTextFieldPlaceholderText: '000.000.000-00',
  nameCheckAuthStepPageLayoutSubtitle:
      'agora precisamos do seu nome e sobrenome para prosseguir',
  phoneCheckAuthStepPageLayoutSubtitle:
      'Vamos te enviar apenas os status dos seus pedidos por WhatsApp, nada de spam :)',
  futurizaButtonTitle: 'provador virtual + visualização 3D',
  futurizaCardTitle: 'provador virtual e 3D',
  futurizaCardSubtitle:
      'teste o produto com realidade aumentada usando a câmera do seu celular ou veja todos os detalhes através da visualização 3D',
  loginSubtitleBottomsheet:
      'em caso de primeiro acesso, selecione entrar apenas com e-mail.',
  loginTitleBottomsheet: 'Escolha sua forma de login',
  loginEmailAndPasswordSubtitle:
      'para acessar sua conta, digite seu email e senha nos campos abaixo.',
  loginEmailAndPasswordButtonText: 'continuar',
  orderConfirmationTitle: 'seu pedido foi concluído com sucesso!',
  orderConfirmationSubtitle: 'você receberá a confirmação do pedido no e-mail:',
  productPixDiscountTag: "preço exclusivo\npara pagamento pix",
  contentDevolutionTitle: 'troca com benefício',
  contentDevolutionSubtitle: 'ganhe 20% na diferença da sua troca em loja!',
  contentDevolutionButton: 'saiba mais',
  bottomsheetDevolutionTitle: '20% na troca? vem pra loja!',
  bottomsheetDevolutionSubtitle:
      'se o valor da sua nova compra for maior do que o valor da peça devolvida, você ganha 20% na diferença. lembra de levar a nota fiscal, tá bem?! esse benefício é exclusivo nas lojas FARM Rio.',
  bottomsheetDevolutionButton: 'ver lojas',
  bottomsheetDevolutionSecondButton: 'prefiro continuar por aqui',
  bottomsheetDevolutionClose: 'fechar',
  exchangeSectionCardTitle: 'Atenção',
  exchangeSectionCardSubtitle:
      '#Nós não cobramos taxas adicionais# para o #transporte# da sua mercadoria!',
);
