import 'package:flutter/material.dart';
import 'package:soma_ui/soma_ui.dart';

import '../screens/splash/circle_transition_clipper.dart';

class TransitionScreen extends StatefulWidget {
  const TransitionScreen({
    super.key,
  });

  @override
  State<TransitionScreen> createState() => _TransitionScreenState();
}

class _TransitionScreenState extends State<TransitionScreen>
    with SingleTickerProviderStateMixin {
  late final animationController =
      AnimationController(vsync: this, duration: const Duration(seconds: 5));

  void setAnimationToTapume() {
    animationController.forward().then((_) {
      Navigator.push(
          context,
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) =>
                const SmTapume(),
            transitionDuration: const Duration(seconds: 2),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) {
              var screenSize = MediaQuery.of(context).size;
              var centerCircleClipper =
                  Offset(screenSize.width / 2, screenSize.height / 2);

              double begingRadius = 0.0;
              double endRadius = screenSize.height * 1.2;
              var radiusTween = Tween(begin: begingRadius, end: endRadius);
              var radiusTweenAnimation = animation.drive(radiusTween);

              return ClipPath(
                clipper: CircleTransitionClipper(
                  center: centerCircleClipper,
                  radius: radiusTweenAnimation.value,
                ),
                child: child,
              );
            },
          ));
    });
  }

  @override
  void initState() {
    super.initState();
    setAnimationToTapume();
  }

  @override
  void dispose() {
    animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // bool shouldGoToTapume = tapume.state.tapumeEtc?.isActive == true;
    // String splash = 'assets/lottie/farm-screen-splash.json';
    String etcSplash = 'assets/lottie/farm-etc-screen-splash.json';
    // final effectiveSplash = shouldGoToTapume
    //     ? etcSplash
    //     : widget.cubit.state.modeEtc
    //         ? etcSplash
    //         : splash;
    return FittedBox(
      fit: BoxFit.cover,
      child: Center(
        child: SmLottie(
          name: etcSplash,
          controller: animationController,
        ),
      ),
    );
  }
}
