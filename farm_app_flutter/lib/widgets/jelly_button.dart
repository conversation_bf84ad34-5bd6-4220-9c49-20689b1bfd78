import 'package:flutter/material.dart';
import 'package:soma_core/config/tapume_controller.dart';
import 'package:soma_ui/soma_ui.dart';

class JellyButton extends StatefulWidget {
  const JellyButton({super.key});

  @override
  State<JellyButton> createState() => _JellyButtonState();
}

class _JellyButtonState extends State<JellyButton>
    with
        SingleTickerProviderStateMixin,
        DesignTokensStateMixin,
        AnalyticsEventDispatcherStateMixin {
  final tapumeRoute = '/tapume';

  late final TapumeController _tapumeController;

  void logSelectContent(String contentType) {
    dispatchSelectContentEvent('home:ameba');
  }

  @override
  Widget build(BuildContext context) {
    const asset = 'assets/images/ameba-farmetc.gif';
    const size = 108.0;

    _tapumeController = context.locateService<TapumeController>();
    final isTapumeActive = _tapumeController.tapumeEtc.isActive == true;
    return isTapumeActive
        ? InkWell(
            onTap: () {
              logSelectContent('home:ameba:ETC');
              navigateToNamed('/transitionEtc');
            },
            child: Image.asset(
              asset,
              height: size,
              width: size,
              gaplessPlayback: true,
            ))
        : SizedBox.shrink();
  }
}
