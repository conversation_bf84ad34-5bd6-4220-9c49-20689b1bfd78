import 'dart:async';
import 'dart:math';

import 'package:farm_app_flutter/widgets/jelly_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:soma_app_commons/widget/home_app_bar.dart';
import 'package:soma_core/soma_core.dart';
import 'package:soma_ui/soma_ui.dart';

class Home extends StatefulWidget {
  const Home({Key? key}) : super(key: key);

  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home>
    with
        AppRoutesStateMixin,
        SomaCoreStateMixin,
        AnalyticsEventDispatcherStateMixin {
  late final newVersion = context.locateService<NewVersion>();
  late final cmsTagController = context.locateService<CmsTagController>();
  double _lastScrollPosition = 0;
  bool _isScrollOnTop = true;
  bool _show = true;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _shouldShowAlertNewVersion();
      cmsTagController.loadCmsTagComponents();
    });

    events.add(UserAuthenticatedEvent(authController.localUserInfo));

    dispatchLogScreenViewEvent(
      screenClass: "Home",
      screenName: "Home",
    );
  }

  Future<void> _shouldShowAlertNewVersion() async {
    try {
      var versionStatus = await newVersion.getVersionStatus();
      if (versionStatus?.canUpdate == true &&
          authController.shouldShowAlertNewVersion == true &&
          config.appFeaturesConfig.storeUpdateApp == true &&
          mounted) {
        newVersion.showUpdateDialog(
          context: context,
          versionStatus: versionStatus!,
        );
      }
      authController.shouldShowAlertNewVersion = false;
    } catch (_) {}
  }

  bool _onScrollBody(ScrollUpdateNotification notification) {
    // Como podem haver mais Scrollables dentro do body, pegamos apenas os eventos
    // de profundidade 0, isto é, eventos que foram disparados pelo primeiro
    // Scrollable da árvore
    final isBodyNotification = notification.depth == 0;
    if (!isBodyNotification) {
      return false;
    }
    final offset = notification.metrics.pixels;
    final ScrollDirection scrollDirection;
    if (_lastScrollPosition < offset) {
      scrollDirection = ScrollDirection.reverse;
    } else if (_lastScrollPosition > offset) {
      scrollDirection = ScrollDirection.forward;
    } else {
      scrollDirection = ScrollDirection.idle;
    }
    final newShow = scrollDirection != ScrollDirection.reverse;
    final newIsScrollOnTop = offset < 200;
    if (newShow != _show || newIsScrollOnTop != _isScrollOnTop) {
      setState(() {
        _show = newShow;
        _isScrollOnTop = newIsScrollOnTop;
      });
    }
    _lastScrollPosition = max(offset, 0);
    return true;
  }

  List<Widget> get homeSurfaceComponents {
    return ModuleRegistry.root
        .pluginsOfType<HomeSurfaceWidgetPlugin>()
        .map((p) => p.build(context))
        .whereType<Widget>()
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return SMCmsContent.forDocument(
      'home',
      layoutBuilder: (_, content) {
        return AnalyticsMetadataProvider(
          metadata: const {
            'screen_name': 'Home',
            'screen_class': 'Home',
          },
          child: Stack(
            children: [
              NestedScrollView(
                floatHeaderSlivers: true,
                headerSliverBuilder: (context, _) => [
                  HomeAppBar(
                    showRigthIcon: false,
                    show: _show,
                    isScrollOnTop: _isScrollOnTop,
                    buttonRightBottomIndex: 2,
                  ),
                ],
                body: NotificationListener<ScrollUpdateNotification>(
                  onNotification: _onScrollBody,
                  child: content,
                ),
              ),
              Positioned(
                top: 110,
                left: 0,
                right: 0,
                child: Column(children: homeSurfaceComponents),
              ),
              const Positioned(
                
                right: 24,
                bottom: 38,
                child: JellyButton(),
              ),
              const Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: SMHomeNotifications(),
              ),
            ],
          ),
        );
      },
    );
  }
}
