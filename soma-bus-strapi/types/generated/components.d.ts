import type { Schema, Attribute } from '@strapi/strapi';

export interface AccordionMenuAccordionMenuEtcItem extends Schema.Component {
  collectionName: 'c_accordion_menu_etc_items';
  info: {
    displayName: 'Accordion Menu New Item';
    icon: 'arrowRight';
    description: '';
  };
  attributes: {
    is_active: Attribute.Boolean & Attribute.DefaultTo<true>;
    title: Attribute.String & Attribute.Required;
    title_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    show_news_tag: Attribute.Boolean & Attribute.DefaultTo<false>;
    navigate_to: Attribute.String;
    tag_text: Attribute.String;
    tag_text_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    tag_background_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    tag_border_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    video_url: Attribute.String;
    image_url: Attribute.String;
    badge_count: Attribute.Integer;
  };
}

export interface AccordionMenuAccordionMenuEtc extends Schema.Component {
  collectionName: 'c_accordion_menu_etcs';
  info: {
    displayName: 'Accordion Menu New';
    icon: 'arrowDown';
    description: '';
  };
  attributes: {
    is_active: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<false>;
    show_new_tag: Attribute.Boolean & Attribute.DefaultTo<false>;
    title: Attribute.String & Attribute.Required;
    title_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    tag: Attribute.Component<'accordion-menu.accordion-menu-tag'>;
    items: Attribute.Component<'accordion-menu.accordion-menu-etc-item', true>;
    navigate_to: Attribute.String;
    image: Attribute.Media;
    badge_count: Attribute.Integer;
  };
}

export interface AccordionMenuAccordionMenuItem extends Schema.Component {
  collectionName: 'c_accordion_menu_items';
  info: {
    displayName: 'Accordion Menu Item';
    icon: 'bulletList';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    navigate_to: Attribute.Text & Attribute.Required;
  };
}

export interface AccordionMenuAccordionMenuTag extends Schema.Component {
  collectionName: 'c_accordion_menu_tags';
  info: {
    displayName: 'Accordion Menu Tag';
    icon: 'priceTag';
    description: '';
  };
  attributes: {
    text: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    text_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    border_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    background_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
  };
}

export interface AccordionMenuAccordionMenu extends Schema.Component {
  collectionName: 'c_accordion_menus';
  info: {
    displayName: 'Accordion Menu';
    icon: 'bulletList';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    banner: Attribute.Media;
    call_to_action: Attribute.String;
    navigate_to: Attribute.Text;
    banner_navigate_to: Attribute.Text;
    tag: Attribute.Component<'accordion-menu.accordion-menu-tag'>;
    items: Attribute.Component<'accordion-menu.accordion-menu-item', true>;
    banner_name_ga4: Attribute.String;
  };
}

export interface AccordionMenuContentMediaMenu extends Schema.Component {
  collectionName: 'components_accordion_menu_content_media_menus';
  info: {
    displayName: 'Menu Media Content';
    icon: 'landscape';
    description: '';
  };
  attributes: {
    image: Attribute.Media;
    video_url: Attribute.String;
    navigate_to: Attribute.String;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface AiAssistantAiAskingSuggestion extends Schema.Component {
  collectionName: 'c_ai_assistant_asking_suggestion';
  info: {
    displayName: 'AI Asking Suggestion';
    icon: 'discuss';
    description: '';
  };
  attributes: {
    ask_suggestion: Attribute.String & Attribute.Required;
  };
}

export interface BadgeDiscountBadgeDiscount extends Schema.Component {
  collectionName: 'c_badge_discounts';
  info: {
    displayName: 'Badge-discount';
    icon: 'picture';
    description: '';
  };
  attributes: {
    coupon: Attribute.String;
    media: Attribute.Media & Attribute.Required;
    background: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface BannerContentTextBannerContentTextDoubleMedia
  extends Schema.Component {
  collectionName: 'c_banner_content_text_double_medias';
  info: {
    displayName: 'Banner Content Text - Double Media';
    icon: 'landscape';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    description: Attribute.String &
      Attribute.Required &
      Attribute.DefaultTo<'subtitle'>;
    cta_text: Attribute.String & Attribute.Required;
    first_media: Attribute.Component<'banner-content-text.banner-content-text-media-item'> &
      Attribute.Required;
    second_media: Attribute.Component<'banner-content-text.banner-content-text-media-item'> &
      Attribute.Required;
    navigate_to: Attribute.String & Attribute.Required;
  };
}

export interface BannerContentTextBannerContentTextMediaItem
  extends Schema.Component {
  collectionName: 'c_banner_content_text_media_items';
  info: {
    displayName: 'Banner Content Text - Media Item';
    icon: 'picture';
    description: '';
  };
  attributes: {
    media: Attribute.Media & Attribute.Required;
    cta_text: Attribute.String;
    navigate_to: Attribute.String;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface BannerContentTextBannerContentText extends Schema.Component {
  collectionName: 'c_banner_content_texts';
  info: {
    displayName: 'Banner Content Text';
    icon: 'command';
    description: '';
  };
  attributes: {
    media: Attribute.Media & Attribute.Required;
    title: Attribute.String & Attribute.Required;
    description: Attribute.String & Attribute.Required;
    text_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    overlay_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    navigate_to: Attribute.String & Attribute.Required;
    variation: Attribute.Enumeration<
      ['overlay', 'medium padding', 'large padding']
    > &
      Attribute.Required &
      Attribute.DefaultTo<'overlay'>;
    cta_text: Attribute.String & Attribute.Required;
    banner: Attribute.String;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface BannerMediaBannerMedia extends Schema.Component {
  collectionName: 'c_banner_medias';
  info: {
    displayName: 'Banner Media';
    icon: 'landscape';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    button_text: Attribute.String & Attribute.Required;
    header_color: Attribute.Enumeration<['claro', 'escuro']> &
      Attribute.Required;
    navigate_to: Attribute.String & Attribute.Required;
    background_image: Attribute.Media;
    video_background: Attribute.Media;
    video_url: Attribute.String;
  };
}

export interface BaseContentFilterList extends Schema.Component {
  collectionName: 'c_filter_lists';
  info: {
    displayName: 'Filter List';
    icon: 'layer';
    description: '';
  };
  attributes: {
    filter: Attribute.String;
  };
}

export interface BaseContentTitleDescription extends Schema.Component {
  collectionName: 'c_title_descriptions';
  info: {
    displayName: 'title_description';
    icon: 'strikeThrough';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.String;
  };
}

export interface BrandSloganLockupBrandSlogan extends Schema.Component {
  collectionName: 'c_brand_slogans';
  info: {
    displayName: 'brand slogan';
    icon: 'command';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    subtitle: Attribute.Text;
    media: Attribute.Media & Attribute.Required;
    background: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    text_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface CallCenterCallCenterCard extends Schema.Component {
  collectionName: 'c_call_center_cards';
  info: {
    displayName: 'call center card';
    icon: 'archive';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    icon: Attribute.Media & Attribute.Required;
    navigate_to: Attribute.String & Attribute.Required;
  };
}

export interface CallCenterCallCenterFaq extends Schema.Component {
  collectionName: 'c_call_center_faqs';
  info: {
    displayName: 'call center faq';
    icon: 'discuss';
    description: '';
  };
  attributes: {
    question: Attribute.String & Attribute.Required;
    answer: Attribute.Text & Attribute.Required;
    link: Attribute.String;
  };
}

export interface CallCenterCards extends Schema.Component {
  collectionName: 'c_call_call_center_cards';
  info: {
    displayName: 'Cards';
    icon: 'archive';
  };
  attributes: {
    Cards: Attribute.Component<'call-center.call-center-card', true>;
  };
}

export interface CallCenterFaq extends Schema.Component {
  collectionName: 'c_call_call_center_faqs';
  info: {
    displayName: 'FAQ';
    icon: 'discuss';
    description: '';
  };
  attributes: {
    itens: Attribute.Component<'call-center.call-center-faq', true>;
  };
}

export interface CarouselBannerCarouselBannerItem extends Schema.Component {
  collectionName: 'c_carousel_banner_items';
  info: {
    displayName: 'Carousel Banner Item';
    description: '';
  };
  attributes: {
    navigate_to: Attribute.Text;
    banner_name_ga4: Attribute.String & Attribute.Required;
    media: Attribute.Media & Attribute.Required;
    video_url: Attribute.String;
  };
}

export interface CarouselBannerCarouselBanner extends Schema.Component {
  collectionName: 'c_carousel_banners';
  info: {
    displayName: 'Carousel Banner';
    icon: 'picture';
    description: '';
  };
  attributes: {
    autoplay: Attribute.Boolean;
    loop: Attribute.Boolean;
    delay: Attribute.Integer &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    items: Attribute.Component<'carousel-banner.carousel-banner-item', true> &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    video_height: Attribute.Decimal;
    video_width: Attribute.Decimal;
  };
}

export interface CarouselListCarouselListButton extends Schema.Component {
  collectionName: 'c_carousel_list_buttons';
  info: {
    displayName: 'Carousel List Button';
    icon: 'link';
    description: '';
  };
  attributes: {
    button_title: Attribute.String & Attribute.Required;
    button_navigate_to: Attribute.String & Attribute.Required;
    has_border: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<false>;
  };
}

export interface CarouselListCarouselListItem extends Schema.Component {
  collectionName: 'c_carousel_list_items';
  info: {
    displayName: 'Carousel List Item';
    icon: 'picture';
    description: '';
  };
  attributes: {
    media: Attribute.Media & Attribute.Required;
    navigate_to: Attribute.Text;
    coupon: Attribute.String;
    banner_name_ga4: Attribute.String & Attribute.Required;
    buttons_link: Attribute.Component<
      'carousel-list.carousel-list-button',
      true
    >;
    secondary_media: Attribute.Component<'carousel-list.double-media-item'> &
      Attribute.SetMinMax<
        {
          min: 2;
          max: 2;
        },
        number
      >;
    clusters: Attribute.String;
    show_to_unlogged_users: Attribute.Boolean;
    video_url: Attribute.String;
  };
}

export interface CarouselListCarouselList extends Schema.Component {
  collectionName: 'c_carousel_lists';
  info: {
    displayName: 'Carousel List';
    icon: 'picture';
    description: '';
  };
  attributes: {
    items: Attribute.Component<'carousel-list.carousel-list-item', true> &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    delay: Attribute.Integer &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    autoplay: Attribute.Boolean;
    loop: Attribute.Boolean;
    title: Attribute.String;
    alignment: Attribute.Enumeration<['Center', 'Higth-Left']> &
      Attribute.DefaultTo<'Center'>;
  };
}

export interface CarouselListDoubleMediaItem extends Schema.Component {
  collectionName: 'c_carousel_secondary_media';
  info: {
    displayName: 'Secondary Media Item';
    icon: 'expand';
    description: '';
  };
  attributes: {
    media: Attribute.Media & Attribute.Required;
    navigate_to: Attribute.String;
    banner_name_ga4: Attribute.String & Attribute.Required;
    coupon: Attribute.String;
  };
}

export interface CashbackMessageCashbackPdp extends Schema.Component {
  collectionName: 'c_message_cashbacks_pdp';
  info: {
    displayName: 'message cashback pdp';
    icon: 'discuss';
    description: '';
  };
  attributes: {
    message: Attribute.String & Attribute.Required;
    message_text_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    is_active: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<false>;
    assignment: Attribute.String;
  };
}

export interface CashbackMessageCashback extends Schema.Component {
  collectionName: 'c_message_cashbacks';
  info: {
    displayName: 'Message cashback';
    icon: 'discuss';
    description: '';
  };
  attributes: {
    message: Attribute.String & Attribute.Required;
    message_text_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    message_backgroud_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    is_active: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<false>;
  };
}

export interface CategoriesMosaicCategoriesMosaicItem extends Schema.Component {
  collectionName: 'c_categories_mosaic_items';
  info: {
    displayName: 'Mosaic Item';
    icon: 'picture';
    description: '';
  };
  attributes: {
    media: Attribute.Media & Attribute.Required;
    navigate_to: Attribute.Text & Attribute.Required;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface CategoriesMosaicCategoriesMosaic extends Schema.Component {
  collectionName: 'c_categories_mosaics';
  info: {
    displayName: 'Categories mosaic';
    icon: 'grid';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    items: Attribute.Component<
      'categories-mosaic.categories-mosaic-item',
      true
    > &
      Attribute.Required;
  };
}

export interface CategoryBannerCategoryBanner extends Schema.Component {
  collectionName: 'c_category_banners';
  info: {
    displayName: 'Category banner';
    icon: 'command';
    description: '';
  };
  attributes: {
    banner_title: Attribute.String;
    banner_subtitle: Attribute.String;
    link_text: Attribute.String;
    bottom_spacing: Attribute.Boolean;
    navigate_to: Attribute.Text & Attribute.Required;
    media: Attribute.Media & Attribute.Required;
    title_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    subtitle_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    link_text_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    has_border: Attribute.Boolean & Attribute.DefaultTo<false>;
    description: Attribute.String;
    banner_name_ga4: Attribute.String & Attribute.Required;
    video_url: Attribute.String;
  };
}

export interface ClockPromotionClockBannerAnimale extends Schema.Component {
  collectionName: 'c_clock_banner_animales';
  info: {
    displayName: 'Banner Clock';
    icon: 'clock';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    subtitle: Attribute.Text;
    coupon: Attribute.String;
    text_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    button_text: Attribute.String;
    button_text_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    button_background_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    media: Attribute.Media;
    navigate_to: Attribute.String;
    start_time: Attribute.DateTime & Attribute.Required;
    end_time: Attribute.DateTime & Attribute.Required;
    clock_position: Attribute.Enumeration<['top', 'center', 'bottom']> &
      Attribute.DefaultTo<'center'>;
    video_url: Attribute.String;
  };
}

export interface ClockPromotionClockBannerNv extends Schema.Component {
  collectionName: 'c_clock_banner_nvs';
  info: {
    displayName: 'Banner Clock';
    icon: 'clock';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    button_text: Attribute.String;
    header_color: Attribute.Enumeration<['claro', 'escuro']> &
      Attribute.Required &
      Attribute.DefaultTo<'claro'>;
    navigate_to: Attribute.Text;
    background_image: Attribute.Media & Attribute.Required;
    coupon: Attribute.String;
    start_time: Attribute.DateTime & Attribute.Required;
    end_time: Attribute.DateTime & Attribute.Required;
    text_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    button_text_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    button_background_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    subtitle: Attribute.String;
  };
}

export interface ClockPromotionClockHeaderAnimale extends Schema.Component {
  collectionName: 'c_clock_header_animales';
  info: {
    displayName: 'General Clock';
    icon: 'clock';
    description: '';
  };
  attributes: {
    clock: Attribute.Component<'clock.clock-animale'> & Attribute.Required;
    pages: Attribute.Component<'clock-promotion.clock-pages-animale', true> &
      Attribute.Required;
  };
}

export interface ClockPromotionClockHeaderCrisbarros extends Schema.Component {
  collectionName: 'c_clock_header_crisbarros';
  info: {
    displayName: 'General Clock';
    icon: 'clock';
  };
  attributes: {
    clock: Attribute.Component<'clock.clock-farm'>;
    pages: Attribute.Component<'clock-promotion.clock-pages-crisbarros', true>;
  };
}

export interface ClockPromotionClockHeaderFarm extends Schema.Component {
  collectionName: 'c_clock_header_farms';
  info: {
    displayName: 'General Clock';
    icon: 'clock';
    description: '';
  };
  attributes: {
    clock: Attribute.Component<'clock.clock-farm'> & Attribute.Required;
    pages: Attribute.Component<'clock-promotion.clock-pages-farm', true> &
      Attribute.Required;
  };
}

export interface ClockPromotionClockHeaderHering extends Schema.Component {
  collectionName: 'c_clock_header_herings';
  info: {
    displayName: 'General Clock';
    icon: 'clock';
    description: '';
  };
  attributes: {
    clock: Attribute.Component<'clock.clock-hering'> & Attribute.Required;
    pages: Attribute.Component<'clock-promotion.clock-pages-hering', true> &
      Attribute.Required;
  };
}

export interface ClockPromotionClockHeaderMariaFilo extends Schema.Component {
  collectionName: 'c_clock_header_maria_filos';
  info: {
    displayName: 'General Clock';
    icon: 'clock';
    description: '';
  };
  attributes: {
    clock: Attribute.Component<'clock.clock-maria-filo'> & Attribute.Required;
    pages: Attribute.Component<'clock-promotion.clock-pages-maria-filo', true> &
      Attribute.Required;
  };
}

export interface ClockPromotionClockHeaderNv extends Schema.Component {
  collectionName: 'c_clock_header_nvs';
  info: {
    displayName: 'General Clock';
    icon: 'clock';
    description: '';
  };
  attributes: {
    clock: Attribute.Component<'clock.clock-nv'> & Attribute.Required;
    pages: Attribute.Component<'clock-promotion.clock-pages-nv', true> &
      Attribute.Required;
  };
}

export interface ClockPromotionClockHeaderOff extends Schema.Component {
  collectionName: 'c_clock_header_offs';
  info: {
    displayName: 'General Clock';
    icon: 'clock';
    description: '';
  };
  attributes: {
    clock: Attribute.Component<'clock.clock-off'> & Attribute.Required;
    pages: Attribute.Component<'clock-promotion.clock-pages-off', true> &
      Attribute.Required;
  };
}

export interface ClockPromotionClockPagesAnimale extends Schema.Component {
  collectionName: 'c_clock_header_pages_animales';
  info: {
    displayName: 'Clock Pages';
    icon: 'file';
    description: '';
  };
  attributes: {
    page: Attribute.Enumeration<
      ['home', 'pdc', 'pdp', 'menu', 'sacola', 'wishlist', 'hub de conteudo']
    > &
      Attribute.Required;
    assignment: Attribute.String;
  };
}

export interface ClockPromotionClockPagesCrisbarros extends Schema.Component {
  collectionName: 'c_clock_header_pages_crisbarros';
  info: {
    displayName: 'Clock Pages';
    icon: 'clock';
  };
  attributes: {
    page: Attribute.Enumeration<['home', 'pdc']> & Attribute.Required;
    assignment: Attribute.String;
  };
}

export interface ClockPromotionClockPagesFarm extends Schema.Component {
  collectionName: 'c_clock_header_pages_farms';
  info: {
    displayName: 'Clock Pages';
    icon: 'file';
    description: '';
  };
  attributes: {
    page: Attribute.Enumeration<['home', 'pdc', 'pdp']> & Attribute.Required;
    assignment: Attribute.String;
  };
}

export interface ClockPromotionClockPagesHering extends Schema.Component {
  collectionName: 'c_clock_header_pages_herings';
  info: {
    displayName: 'Clock Pages';
    icon: 'file';
    description: '';
  };
  attributes: {
    page: Attribute.Enumeration<['home', 'pdc']> & Attribute.Required;
    assignment: Attribute.String;
  };
}

export interface ClockPromotionClockPagesMariaFilo extends Schema.Component {
  collectionName: 'c_clock_header_pages_maria_filos';
  info: {
    displayName: 'Clock Pages';
    icon: 'file';
    description: '';
  };
  attributes: {
    page: Attribute.Enumeration<['home', 'pdc']> & Attribute.Required;
    assignment: Attribute.String;
  };
}

export interface ClockPromotionClockPagesNv extends Schema.Component {
  collectionName: 'c_clock_header_pages_nvs';
  info: {
    displayName: 'Clock Pages';
    icon: 'file';
    description: '';
  };
  attributes: {
    page: Attribute.Enumeration<['home', 'pdc', 'pdp']> & Attribute.Required;
    assignment: Attribute.String;
  };
}

export interface ClockPromotionClockPagesOff extends Schema.Component {
  collectionName: 'c_clock_header_pages_offs';
  info: {
    displayName: 'Clock Pages';
    icon: 'file';
    description: '';
  };
  attributes: {
    page: Attribute.Enumeration<['home', 'pdc']> & Attribute.Required;
    assignment: Attribute.String;
  };
}

export interface ClockPromotionClockProductFarm extends Schema.Component {
  collectionName: 'c_clock_header_product_farms';
  info: {
    displayName: 'Product Clock';
    icon: 'clock';
    description: '';
  };
  attributes: {
    is_active: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<false>;
    assignment: Attribute.String & Attribute.Required;
    coupon: Attribute.String;
    button_text: Attribute.String;
    text_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    background_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    start_time: Attribute.DateTime & Attribute.Required;
    end_time: Attribute.DateTime & Attribute.Required;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface ClockPromotionClockProductHering extends Schema.Component {
  collectionName: 'c_clock_header_product_herings';
  info: {
    displayName: 'Product Clock';
    icon: 'clock';
    description: '';
  };
  attributes: {
    is_active: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<false>;
    product_assignment: Attribute.String;
    coupon: Attribute.String;
    button_text: Attribute.String;
    text_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    background_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    start_time: Attribute.DateTime & Attribute.Required;
    end_time: Attribute.DateTime & Attribute.Required;
    banner_name_ga4: Attribute.String & Attribute.Required;
    title: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 71;
      }>;
    position: Attribute.Enumeration<
      ['Ap\u00F3s o seletor de tamanho', "Ap\u00F3s 'Sobre a pe\u00E7a'"]
    > &
      Attribute.Required &
      Attribute.DefaultTo<'Ap\u00F3s o seletor de tamanho'>;
    cluster_assignment: Attribute.String;
    pages_to_show_clock: Attribute.Enumeration<
      ['Ambas as p\u00E1ginas', 'PDP', 'PDC']
    > &
      Attribute.DefaultTo<'Ambas as p\u00E1ginas'>;
  };
}

export interface ClockClockAnimale extends Schema.Component {
  collectionName: 'c_clock_animales';
  info: {
    displayName: 'Clock';
    icon: 'clock';
    description: '';
  };
  attributes: {
    is_active: Attribute.Boolean & Attribute.DefaultTo<false>;
    navigate_to: Attribute.Text;
    coupon: Attribute.String;
    button_text: Attribute.String;
    start_time: Attribute.DateTime & Attribute.Required;
    end_time: Attribute.DateTime & Attribute.Required;
    text_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    background_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    subtitle: Attribute.Text;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface ClockClockCrisbarros extends Schema.Component {
  collectionName: 'c_clock_crisbarros';
  info: {
    displayName: 'Clock';
    icon: 'clock';
  };
  attributes: {
    is_active: Attribute.Boolean;
    navigate_to: Attribute.String;
    coupon: Attribute.String;
    button_text: Attribute.String;
    start_time: Attribute.DateTime & Attribute.Required;
    end_time: Attribute.DateTime & Attribute.Required;
    text_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    background_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    subtitle: Attribute.String;
  };
}

export interface ClockClockFarm extends Schema.Component {
  collectionName: 'c_clock_farms';
  info: {
    displayName: 'Clock';
    icon: 'clock';
    description: '';
  };
  attributes: {
    is_active: Attribute.Boolean & Attribute.DefaultTo<false>;
    coupon: Attribute.String;
    button_text: Attribute.String;
    title: Attribute.Text;
    start_time: Attribute.DateTime & Attribute.Required;
    end_time: Attribute.DateTime & Attribute.Required;
    text_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    background_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    button_position: Attribute.Enumeration<
      ['abaixo do rel\u00F3gio', 'depois do cupom']
    >;
    subtitle: Attribute.String;
    navigate_to: Attribute.Text;
    banner_name_ga4: Attribute.String & Attribute.Required;
    show_cronometer: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<true>;
    coupon_button_text_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
  };
}

export interface ClockClockHering extends Schema.Component {
  collectionName: 'c_clock_herings';
  info: {
    displayName: 'Clock';
    icon: 'clock';
    description: '';
  };
  attributes: {
    is_active: Attribute.Boolean & Attribute.DefaultTo<false>;
    title: Attribute.String & Attribute.Required;
    rules_button_text: Attribute.String;
    rules_description: Attribute.Text;
    navigate_to: Attribute.Text;
    coupon: Attribute.String;
    button_text: Attribute.String;
    start_time: Attribute.DateTime & Attribute.Required;
    end_time: Attribute.DateTime & Attribute.Required;
    text_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    background_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    subtitle: Attribute.String;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface ClockClockMariaFilo extends Schema.Component {
  collectionName: 'c_clock_maria_filos';
  info: {
    displayName: 'Clock';
    icon: 'clock';
    description: '';
  };
  attributes: {
    is_active: Attribute.Boolean & Attribute.DefaultTo<false>;
    title: Attribute.String & Attribute.Required;
    navigate_to: Attribute.Text;
    coupon: Attribute.String;
    button_text: Attribute.String;
    start_time: Attribute.DateTime & Attribute.Required;
    end_time: Attribute.DateTime & Attribute.Required;
    text_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    background_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    banner_name_ga4: Attribute.String & Attribute.Required;
    subtitle: Attribute.String;
    rules_button_text: Attribute.String;
    rules_description: Attribute.Text;
    show_coupon: Attribute.Boolean & Attribute.DefaultTo<true>;
  };
}

export interface ClockClockNv extends Schema.Component {
  collectionName: 'c_clock_nvs';
  info: {
    displayName: 'Clock';
    icon: 'clock';
    description: '';
  };
  attributes: {
    is_active: Attribute.Boolean & Attribute.DefaultTo<false>;
    navigate_to: Attribute.Text;
    coupon: Attribute.String;
    button_text: Attribute.String;
    start_time: Attribute.DateTime & Attribute.Required;
    end_time: Attribute.DateTime & Attribute.Required;
    text_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    background_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    title: Attribute.String & Attribute.Required;
    button_background_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    button_text_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    subtitle: Attribute.String;
  };
}

export interface ClockClockOff extends Schema.Component {
  collectionName: 'c_clock_offs';
  info: {
    displayName: 'Clock';
    icon: 'clock';
    description: '';
  };
  attributes: {
    is_active: Attribute.Boolean & Attribute.DefaultTo<false>;
    title: Attribute.String & Attribute.Required;
    rules_description: Attribute.Text;
    navigate_to: Attribute.Text;
    start_time: Attribute.DateTime & Attribute.Required;
    end_time: Attribute.DateTime & Attribute.Required;
    text_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    background_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    subtitle: Attribute.Text;
    rules_button_text: Attribute.String;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface ClustersComponentsCategoryBannerCluster
  extends Schema.Component {
  collectionName: 'c_category_banner_clusters';
  info: {
    displayName: 'Category Banner - Cluster';
    icon: 'scissors';
    description: '';
  };
  attributes: {
    category_banner: Attribute.Component<'category-banner.category-banner'> &
      Attribute.Required;
    clusters: Attribute.Component<'text-content.list-text', true>;
  };
}

export interface ClustersComponentsCouponBannerCluster
  extends Schema.Component {
  collectionName: 'c_coupon_banner_clusters';
  info: {
    displayName: 'Coupon Banner - Cluster';
    icon: 'gift';
    description: '';
  };
  attributes: {
    clusters: Attribute.String;
    coupon_banner: Attribute.Component<'coupon-banner.coupon-banner'>;
    show_to_unlogged_users: Attribute.Boolean & Attribute.DefaultTo<true>;
  };
}

export interface ClustersComponentsGeneralClockCluster
  extends Schema.Component {
  collectionName: 'c_general_clock_clusters';
  info: {
    displayName: 'General Clock - Cluster';
    icon: 'clock';
    description: '';
  };
  attributes: {
    clock: Attribute.Component<'clock.clock-hering'> & Attribute.Required;
    pages: Attribute.Component<'clock-promotion.clock-pages-hering', true> &
      Attribute.Required;
    clusters: Attribute.String;
    show_to_unlogged_users: Attribute.Boolean & Attribute.DefaultTo<true>;
  };
}

export interface ClustersComponentsMessageBannerClusters
  extends Schema.Component {
  collectionName: 'c_message_banner_clusters';
  info: {
    displayName: 'Message Banner - Clusters';
    icon: 'bell';
    description: '';
  };
  attributes: {
    message_banner: Attribute.Component<'notification-center.message-banner'> &
      Attribute.Required;
    clusters: Attribute.String & Attribute.Required;
  };
}

export interface ClustersComponentsMessageClusters extends Schema.Component {
  collectionName: 'c_message_clusters';
  info: {
    displayName: 'Message - Clusters';
    icon: 'bell';
    description: '';
  };
  attributes: {
    message: Attribute.Component<'notification-center.message'> &
      Attribute.Required;
    clusters: Attribute.String & Attribute.Required;
  };
}

export interface ClustersComponentsProductClockCluster
  extends Schema.Component {
  collectionName: 'c_product_clock_clusters';
  info: {
    displayName: 'Product Clock - Cluster';
    icon: 'clock';
    description: '';
  };
  attributes: {
    clock: Attribute.Component<'clock-promotion.clock-product-hering'> &
      Attribute.Required;
    clusters: Attribute.String;
    show_to_unlogged_users: Attribute.Boolean & Attribute.DefaultTo<true>;
  };
}

export interface ComboPromotionCarouselComboPromotion extends Schema.Component {
  collectionName: 'c_carousel_combo_promotions';
  info: {
    displayName: 'carousel combo promotion';
    icon: 'cog';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.String;
    list_combo: Attribute.Component<'combo-promotion.repetable-combo', true>;
    filter: Attribute.String;
  };
}

export interface ComboPromotionComboSteps extends Schema.Component {
  collectionName: 'components_combo_promotion_combo_steps';
  info: {
    displayName: 'Combo PDP Steps';
    icon: 'handHeart';
    description: '';
  };
  attributes: {
    filter_category_or_cluster: Attribute.String;
    product_ids: Attribute.String;
    items_quantity: Attribute.Integer & Attribute.Required;
    combo_title: Attribute.String & Attribute.Required;
    combo_info_message: Attribute.String & Attribute.Required;
    tag_text: Attribute.String;
    tag_color_text: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
  };
}

export interface ComboPromotionCombo extends Schema.Component {
  collectionName: 'c_combos';
  info: {
    displayName: 'combo';
    icon: 'handHeart';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.String;
    media_title: Attribute.String;
    media_title_text_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    media: Attribute.Media & Attribute.Required;
    tag: Attribute.String;
    card_content_title: Attribute.String;
    card_content_description: Attribute.String;
    call_to_action: Attribute.String;
    call_to_action_navigate_to: Attribute.String;
    coupon: Attribute.String;
    bottom_sheet_call_to_action: Attribute.String;
    bottom_sheet_button_text: Attribute.String;
    bottom_sheet_button_navigate_to: Attribute.String;
    bottom_sheet_content: Attribute.String;
    filter: Attribute.String;
    banner_name_ga4: Attribute.String & Attribute.Required;
    video_url: Attribute.String;
  };
}

export interface ComboPromotionRepetableCombo extends Schema.Component {
  collectionName: 'c_repetable_combos';
  info: {
    displayName: 'repetable-combo';
    icon: 'apps';
    description: '';
  };
  attributes: {
    media_title: Attribute.String;
    media_title_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    media: Attribute.Media & Attribute.Required;
    tag: Attribute.String;
    card_content_title: Attribute.String;
    card_content_description: Attribute.String;
    call_to_action: Attribute.String;
    call_to_action_navigate_to: Attribute.String;
    coupon: Attribute.String;
    bottom_sheet_call_to_action: Attribute.String;
    bottom_sheet_content: Attribute.String;
    bottom_sheet_button_text: Attribute.String;
    bottom_sheet_button_navigate_to: Attribute.String;
    banner_name_ga4: Attribute.String & Attribute.Required;
    video_url: Attribute.String;
  };
}

export interface ComponentsComponents extends Schema.Component {
  collectionName: 'c_components';
  info: {
    displayName: 'components';
    icon: 'picture';
  };
  attributes: {};
}

export interface ContentAreaContentAreaBannerItem extends Schema.Component {
  collectionName: 'c_banner_items';
  info: {
    displayName: 'Banner-Item';
    icon: 'landscape';
    description: '';
  };
  attributes: {
    banner_title: Attribute.String & Attribute.Required;
    navigate_to: Attribute.String & Attribute.Required;
    banner_image: Attribute.Media & Attribute.Required;
  };
}

export interface ContentAreaContentAreaWithSign extends Schema.Component {
  collectionName: 'c_content_area_with_signs';
  info: {
    displayName: 'Content Area with sign';
    icon: 'apps';
    description: '';
  };
  attributes: {
    signUp: Attribute.Component<'sign-up-login.sign-up-login'>;
    items: Attribute.Component<'content-area.content-area-banner-item', true>;
  };
}

export interface ContentAreaContentArea extends Schema.Component {
  collectionName: 'c_content_areas';
  info: {
    displayName: 'Content Area';
    icon: 'apps';
    description: '';
  };
  attributes: {
    title_logged_in: Attribute.String & Attribute.Required;
    description_logged_in: Attribute.String & Attribute.Required;
    title_not_logged_in: Attribute.String & Attribute.Required;
    description_not_logged_in: Attribute.String & Attribute.Required;
    shortcut_first: Attribute.Enumeration<
      ['Concierge', 'Saldo dispon\u00EDvel', 'NV Club']
    > &
      Attribute.Required;
    shortcut_second: Attribute.Enumeration<
      ['Concierge', 'Saldo dispon\u00EDvel', 'NV Club', 'Nenhum']
    > &
      Attribute.Required &
      Attribute.DefaultTo<'Nenhum'>;
    shortcut_third: Attribute.Enumeration<
      ['Concierge', 'Saldo dispon\u00EDvel', 'NV Club', 'Nenhum']
    > &
      Attribute.Required &
      Attribute.DefaultTo<'Nenhum'>;
    items: Attribute.Component<'content-area.content-area-banner-item', true> &
      Attribute.Required;
  };
}

export interface CouponBannerCouponBanner extends Schema.Component {
  collectionName: 'c_coupon_banners';
  info: {
    displayName: 'Coupon Banner';
    icon: 'gift';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.String;
    coupon: Attribute.String & Attribute.Required;
    background_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    text_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    has_margin: Attribute.Boolean;
    navigate_to: Attribute.Text;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface CreatedSuitcaseEmptyCreatedSuitcaseEmpty
  extends Schema.Component {
  collectionName: 'components_created_suitcase_empty_created_suitcase_empties';
  info: {
    displayName: 'Created Suitcase Empty';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    button_title: Attribute.String & Attribute.Required;
    subtitle: Attribute.String & Attribute.Required;
    filter_category_or_cluster: Attribute.String & Attribute.Required;
  };
}

export interface CuponsCupom extends Schema.Component {
  collectionName: 'c_cupoms';
  info: {
    displayName: 'Cupom';
    icon: 'medium';
    description: '';
  };
  attributes: {
    pages: Attribute.Component<'clock-promotion.clock-pages-animale', true> &
      Attribute.Required;
    coupon: Attribute.String;
    title: Attribute.String;
    subtitle: Attribute.String;
    text_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    background_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    is_active: Attribute.Boolean & Attribute.DefaultTo<false>;
  };
}

export interface DeeplinkDeeplinks extends Schema.Component {
  collectionName: 'components_deeplink_deeplinks';
  info: {
    displayName: 'Deeplinks';
    icon: 'link';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    url: Attribute.String & Attribute.Required;
    filterCategoryOrCluster: Attribute.String;
    order_by_intelligent_search: Attribute.Enumeration<
      [
        'OrderByBestDiscountDESC',
        'OrderByPriceASC',
        'OrderByPriceDESC',
        'OrderByReleaseDateDESC',
        'OrderByTopSaleDESC'
      ]
    >;
  };
}

export interface DeeplinkInvalidDeeplink extends Schema.Component {
  collectionName: 'components_deeplink_invalid_deeplinks';
  info: {
    displayName: 'Invalid Deeplink';
    icon: 'scissors';
    description: '';
  };
  attributes: {
    invalid_deeplink: Attribute.String & Attribute.Required;
  };
}

export interface EtcBadgeDiscount extends Schema.Component {
  collectionName: 'components_etc_badge_discounts';
  info: {
    displayName: 'Badge Discount';
    icon: 'scissors';
  };
  attributes: {
    coupon: Attribute.String & Attribute.Required;
    discount: Attribute.String;
    image_logo: Attribute.Media;
  };
}

export interface EtcHeaderEtc extends Schema.Component {
  collectionName: 'components_etc_header_etcs';
  info: {
    displayName: 'Header ETC';
    icon: 'puzzle';
    description: '';
  };
  attributes: {
    background_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    foreground_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    image: Attribute.Media;
  };
}

export interface EtcHomeComponent extends Schema.Component {
  collectionName: 'c_home_components';
  info: {
    displayName: 'Home Component';
    icon: 'chartBubble';
    description: '';
  };
  attributes: {
    media: Attribute.Media & Attribute.Required;
    navigate_to: Attribute.String;
    button_text: Attribute.String;
    button_navigate_to: Attribute.String;
    has_border: Attribute.Boolean & Attribute.DefaultTo<false>;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface EtcHomeComposition extends Schema.Component {
  collectionName: 'c_home_compositions';
  info: {
    displayName: 'Home Composition';
    icon: 'command';
    description: '';
  };
  attributes: {
    home_components: Attribute.Component<'etc.home-component', true>;
  };
}

export interface EtcImageContent extends Schema.Component {
  collectionName: 'c_image_contents';
  info: {
    displayName: 'Image Content';
    icon: 'landscape';
    description: '';
  };
  attributes: {
    media: Attribute.Media;
    button_text: Attribute.String;
    navigate_to: Attribute.String;
    video_url: Attribute.String;
    button_navigate_to: Attribute.String;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface EtcProductsGridFilter extends Schema.Component {
  collectionName: 'c_products_grid_filters';
  info: {
    displayName: 'Products Grid Filter';
    icon: 'apps';
    description: '';
  };
  attributes: {
    items: Attribute.Component<'showcase-list.show-case-list-item', true>;
    title: Attribute.String;
    subtitle: Attribute.String;
    call_to_action: Attribute.String;
    filter_category_or_cluster: Attribute.String;
    order_by_intelligent_search: Attribute.Enumeration<
      [
        'OrderByBestDiscountDESC',
        'OrderByPriceASC',
        'OrderByPriceDESC',
        'OrderByReleaseDateDESC',
        'OrderByTopSaleDESC'
      ]
    >;
    background_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    foreground_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface FidelityBenefitsItem extends Schema.Component {
  collectionName: 'c_benefits_items';
  info: {
    displayName: 'Benefits Item';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    description: Attribute.String & Attribute.Required;
  };
}

export interface FidelityBenefits extends Schema.Component {
  collectionName: 'c_benefits';
  info: {
    displayName: 'Benefits';
    icon: 'gift';
  };
  attributes: {
    items: Attribute.Component<'fidelity.benefits-item', true> &
      Attribute.Required;
  };
}

export interface FidelityClubExample extends Schema.Component {
  collectionName: 'c_club_examples';
  info: {
    displayName: 'club example';
    icon: 'handHeart';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    purchase_image: Attribute.Media & Attribute.Required;
    background_image: Attribute.Media & Attribute.Required;
  };
}

export interface FidelityFaqItem extends Schema.Component {
  collectionName: 'c_faq_items';
  info: {
    displayName: 'FAQ item';
    icon: 'bulletList';
    description: '';
  };
  attributes: {
    faq_question: Attribute.String & Attribute.Required;
    faq_answer: Attribute.Text & Attribute.Required;
  };
}

export interface FidelityFaq extends Schema.Component {
  collectionName: 'c_faqs';
  info: {
    displayName: 'FAQ';
    icon: 'bulletList';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    background_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    items: Attribute.Component<'fidelity.faq-item', true> & Attribute.Required;
  };
}

export interface FidelityHowWorkItem extends Schema.Component {
  collectionName: 'c_how_work_items';
  info: {
    displayName: 'How Work Item';
    icon: 'connector';
  };
  attributes: {
    step_title: Attribute.String & Attribute.Required;
    step_description: Attribute.String & Attribute.Required;
    step_number: Attribute.String & Attribute.Required;
    redirect_to_login: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<false>;
  };
}

export interface FidelityHowWork extends Schema.Component {
  collectionName: 'c_how_works';
  info: {
    displayName: 'How Work';
    icon: 'connector';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    items: Attribute.Component<'fidelity.how-work-item', true>;
  };
}

export interface FidelityInvite extends Schema.Component {
  collectionName: 'c_invites';
  info: {
    displayName: 'Invite';
    icon: 'emotionHappy';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    button_text: Attribute.String;
    coupon: Attribute.String;
    coupon_button_text: Attribute.String;
  };
}

export interface FidelityJoinClub extends Schema.Component {
  collectionName: 'c_join_clubs';
  info: {
    displayName: 'join club';
    icon: 'command';
    description: '';
  };
  attributes: {
    background_image: Attribute.Media & Attribute.Required;
  };
}

export interface FidelityLevelDescriptionItem extends Schema.Component {
  collectionName: 'c_level_description_items';
  info: {
    displayName: 'Level Description Item';
    icon: 'filter';
    description: '';
  };
  attributes: {
    level_name: Attribute.String & Attribute.Required;
    level_list_benefits: Attribute.Text & Attribute.Required;
    level_points_range: Attribute.String & Attribute.Required;
  };
}

export interface FidelityLevelDescription extends Schema.Component {
  collectionName: 'c_level_description';
  info: {
    displayName: 'Level Description';
    icon: 'filter';
    description: '';
  };
  attributes: {
    levels_title: Attribute.String & Attribute.Required;
    levels_description: Attribute.String & Attribute.Required;
    center_content: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<false>;
    show_user_level: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<false>;
    background_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    items: Attribute.Component<'fidelity.level-description-item', true> &
      Attribute.Required;
  };
}

export interface FidelityPointsForRegister extends Schema.Component {
  collectionName: 'c_points_for_registers';
  info: {
    displayName: 'Points For Register';
    icon: 'handHeart';
  };
  attributes: {
    title_complete_register: Attribute.String & Attribute.Required;
    button_text_complete_register: Attribute.String & Attribute.Required;
    title_completed_register: Attribute.String & Attribute.Required;
    button_text_completed_register: Attribute.String & Attribute.Required;
  };
}

export interface FidelityRateClub extends Schema.Component {
  collectionName: 'c_rate_clubs';
  info: {
    displayName: 'Rate Club';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    navigate_to: Attribute.String & Attribute.Required;
  };
}

export interface FidelityRedirectButton extends Schema.Component {
  collectionName: 'c_redirect_buttons';
  info: {
    displayName: 'redirect button';
    icon: 'arrowRight';
    description: '';
  };
  attributes: {
    button_text: Attribute.String & Attribute.Required;
    navigate_to: Attribute.String & Attribute.Required;
  };
}

export interface FidelityUserInfo extends Schema.Component {
  collectionName: 'c_user_infos';
  info: {
    displayName: 'User Info';
    icon: 'user';
    description: '';
  };
  attributes: {
    show_user_club_info: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<false>;
    items: Attribute.Component<'fidelity.level-description-item', true>;
  };
}

export interface FullLookCarouselFullLookCarouselItem extends Schema.Component {
  collectionName: 'c_full_look_carousel_items';
  info: {
    displayName: 'Full Look Carousel Item';
    description: '';
  };
  attributes: {
    image: Attribute.Media & Attribute.Required;
    product_ids: Attribute.Text & Attribute.Required;
    banner_name_ga4: Attribute.String & Attribute.Required;
    button_text: Attribute.String;
  };
}

export interface FullLookCarouselFullLookCarousel extends Schema.Component {
  collectionName: 'c_full_look_carousels';
  info: {
    displayName: 'Full Look Carousel';
    icon: 'landscape';
    description: '';
  };
  attributes: {
    items: Attribute.Component<
      'full-look-carousel.full-look-carousel-item',
      true
    >;
    title: Attribute.String &
      Attribute.Required &
      Attribute.DefaultTo<'Complete o look'>;
    subtitle: Attribute.String;
    call_to_action: Attribute.String;
  };
}

export interface GridCategoryListingGridCategoryListing
  extends Schema.Component {
  collectionName: 'c_grid_category_listings';
  info: {
    displayName: 'Grid Category Listing';
    icon: 'command';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    subtitle: Attribute.Text;
    call_to_action: Attribute.String;
    navigate_to: Attribute.String;
    background: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    items: Attribute.Component<'grid-category-listing.grind-item', true> &
      Attribute.Required;
  };
}

export interface GridCategoryListingGrindItem extends Schema.Component {
  collectionName: 'c_grind_items';
  info: {
    displayName: 'Grind item';
    icon: 'apps';
    description: '';
  };
  attributes: {
    navigate_to: Attribute.String & Attribute.Required;
    media: Attribute.Media & Attribute.Required;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface HighlightHighlightList extends Schema.Component {
  collectionName: 'c_highlight_lists';
  info: {
    displayName: 'Highlight List';
    description: '';
  };
  attributes: {
    items: Attribute.Component<'highlight.highlight', true>;
  };
}

export interface HighlightHighlightOptionalItem extends Schema.Component {
  collectionName: 'c_highlight_optional_items';
  info: {
    displayName: 'Highlight optional item';
    icon: 'filter';
  };
  attributes: {
    image: Attribute.Media;
    title: Attribute.String & Attribute.Required;
    subtitle: Attribute.String & Attribute.Required;
  };
}

export interface HighlightHighlightOptionalMedia extends Schema.Component {
  collectionName: 'c_highlight_optional_medias';
  info: {
    displayName: 'Highlight optional media';
    icon: 'bulletList';
  };
  attributes: {
    items: Attribute.Component<'highlight.highlight-optional-item', true>;
  };
}

export interface HighlightHighlight extends Schema.Component {
  collectionName: 'c_highlights';
  info: {
    displayName: 'Highlight List Item';
    description: '';
  };
  attributes: {
    image: Attribute.Media & Attribute.Required;
    title: Attribute.String & Attribute.Required;
    subtitle: Attribute.Text & Attribute.Required;
  };
}

export interface HrgComponentsHrgBannerItem extends Schema.Component {
  collectionName: 'c_hrg_banner_item';
  info: {
    displayName: 'HRG Banner Item';
  };
  attributes: {
    navigate_to: Attribute.String;
    banner_name_ga4: Attribute.String;
    video_url: Attribute.String;
    media: Attribute.Media;
    title: Attribute.String;
    subtitle: Attribute.String;
    call_to_action: Attribute.String;
    store_window: Attribute.Component<
      'hrg-components.hrg-store-window-item',
      true
    >;
  };
}

export interface HrgComponentsHrgCarouselBanner extends Schema.Component {
  collectionName: 'c_hrg_carousel_banners';
  info: {
    displayName: 'HRG Carousel Banner';
    description: '';
  };
  attributes: {
    autoplay: Attribute.Boolean;
    title: Attribute.String;
    cta_text: Attribute.String;
    cta_color: Attribute.String;
    navigate_to: Attribute.String;
    items: Attribute.Component<'hrg-components.hrg-banner-item', true> &
      Attribute.SetMinMax<
        {
          max: 10;
        },
        number
      >;
  };
}

export interface HrgComponentsHrgCategoriesBanners extends Schema.Component {
  collectionName: 'c_hrg_c_hrg_categories_banners';
  info: {
    displayName: 'HRG Categories Banners';
    icon: 'picture';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    banners: Attribute.Component<'hrg-components.hrg-category-banner', true>;
  };
}

export interface HrgComponentsHrgCategoryBanner extends Schema.Component {
  collectionName: 'c_hrg_c_hrg_category_banner';
  info: {
    displayName: 'HRG Category Banner';
    icon: 'picture';
    description: '';
  };
  attributes: {
    description: Attribute.String;
    media: Attribute.Media & Attribute.Required;
    navigate_to: Attribute.String & Attribute.Required;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface HrgComponentsHrgDynamicNavigationBannerItem
  extends Schema.Component {
  collectionName: 'c_hrg_dynamic_navigation_banner_item';
  info: {
    displayName: 'HRG Dynamic Navigation Banner Item';
    icon: 'picture';
    description: '';
  };
  attributes: {
    media: Attribute.Media & Attribute.Required;
    description: Attribute.String & Attribute.Required;
    banner_name_ga4: Attribute.String & Attribute.Required;
    navigate_to: Attribute.String & Attribute.Required;
  };
}

export interface HrgComponentsHrgDynamicNavigationBanners
  extends Schema.Component {
  collectionName: 'c_hrg_dynamic_navigation_banners';
  info: {
    displayName: 'HRG Dynamic Navigation Banners';
    icon: 'search';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    items: Attribute.Component<
      'hrg-components.hrg-dynamic-navigation-banner-item',
      true
    >;
  };
}

export interface HrgComponentsHrgMenuCollectionsGroup extends Schema.Component {
  collectionName: 'c_hrg_menu_collections_groups';
  info: {
    displayName: 'HRG Menu Collections Group';
    icon: 'layer';
    description: '';
  };
  attributes: {
    collection_title: Attribute.String;
    collection_title_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    items: Attribute.Component<'hrg-components.hrg-menu-list-item', true> &
      Attribute.Required;
  };
}

export interface HrgComponentsHrgMenuContentHeaderBanners
  extends Schema.Component {
  collectionName: 'c_hrg_menu_content_header_banners';
  info: {
    displayName: 'HRG Menu Content Header Banners';
    icon: 'landscape';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    banners: Attribute.Component<'hrg-components.hrg-category-banner', true>;
  };
}

export interface HrgComponentsHrgMenuFloatingButton extends Schema.Component {
  collectionName: 'c_hrg_menu_floating_buttons';
  info: {
    displayName: 'HRG Menu Floating Button';
    icon: 'lightbulb';
    description: '';
  };
  attributes: {
    text: Attribute.String & Attribute.Required;
    navigate_to: Attribute.String & Attribute.Required;
  };
}

export interface HrgComponentsHrgMenuListItem extends Schema.Component {
  collectionName: 'c_hrg_menu_list_items';
  info: {
    displayName: 'HRG Menu List Item';
    icon: 'server';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    title_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    navigate_to: Attribute.String;
  };
}

export interface HrgComponentsHrgMenuSegmentItem extends Schema.Component {
  collectionName: 'c_hrg_menu_segment_items';
  info: {
    displayName: 'HRG Menu Segment Item';
    icon: 'bulletList';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    navigate_to: Attribute.String;
    collections_group_page_title: Attribute.String;
    collections_groups: Attribute.Component<
      'hrg-components.hrg-menu-collections-group',
      true
    >;
    tag_text: Attribute.String;
    title_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
  };
}

export interface HrgComponentsHrgMenuSegmentSection extends Schema.Component {
  collectionName: 'c_hrg_menu_segment_sections';
  info: {
    displayName: 'HRG Menu Segment Section';
    icon: 'bulletList';
  };
  attributes: {
    section_title: Attribute.String;
    items: Attribute.Component<'hrg-components.hrg-menu-segment-item', true> &
      Attribute.Required;
    section_title_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
  };
}

export interface HrgComponentsHrgMenuTab extends Schema.Component {
  collectionName: 'c_hrg_menu_tabs';
  info: {
    displayName: 'HRG Menu Tab';
    icon: 'filter';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    content: Attribute.Relation<
      'hrg-components.hrg-menu-tab',
      'oneToOne',
      'api::hrg-menu-contents-hering.hrg-menu-contents-hering'
    >;
  };
}

export interface HrgComponentsHrgNewArrivalsSection extends Schema.Component {
  collectionName: 'c_hrg_new_arrivals_sections';
  info: {
    displayName: 'HRG New Arrivals Section';
    icon: 'shirt';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    media: Attribute.Media;
    navigate_to: Attribute.String & Attribute.Required;
    show_anchor_button: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<true>;
  };
}

export interface HrgComponentsHrgProductCharacteristic
  extends Schema.Component {
  collectionName: 'c_hrg_components_hrg_product_characteristics';
  info: {
    displayName: 'HRG Product Characteristic';
    description: '';
  };
  attributes: {
    icon: Attribute.Media;
    title: Attribute.String;
    helper_text: Attribute.Text;
    gauge: Attribute.Integer &
      Attribute.SetMinMax<
        {
          min: 0;
          max: 5;
        },
        number
      >;
  };
}

export interface HrgComponentsHrgProductColor extends Schema.Component {
  collectionName: 'c_hrg_components_hrg_product_colors';
  info: {
    displayName: 'HRG Product Color';
  };
  attributes: {
    color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
  };
}

export interface HrgComponentsHrgProductMenuCarousel extends Schema.Component {
  collectionName: 'c_hrg_components_hrg_product_menu_carousels';
  info: {
    displayName: 'HRG Product Menu Carousel';
  };
  attributes: {
    products: Attribute.Component<'hrg-components.hrg-product-menu', true>;
  };
}

export interface HrgComponentsHrgProductMenu extends Schema.Component {
  collectionName: 'c_hrg_components_hrg_product_menus';
  info: {
    displayName: 'HRG Product Menu';
    description: '';
  };
  attributes: {
    media: Attribute.Media;
    title: Attribute.String;
    call_to_action: Attribute.String;
    navigate_to: Attribute.String;
    product_characteristics: Attribute.Component<
      'hrg-components.hrg-product-characteristic',
      true
    >;
    product_colors: Attribute.Component<
      'hrg-components.hrg-product-color',
      true
    >;
    banner_name_ga4: Attribute.String;
    combo_description: Attribute.String;
  };
}

export interface HrgComponentsHrgSpotProduct extends Schema.Component {
  collectionName: 'c_hrg_c_spot_products';
  info: {
    displayName: 'HRG Spot Product';
    icon: 'landscape';
    description: '';
  };
  attributes: {
    filter_category_or_cluster: Attribute.String & Attribute.Required;
    title: Attribute.String & Attribute.Required;
    orderby: Attribute.Enumeration<
      [
        'OrderByBestDiscountDESC',
        'OrderByPriceASC',
        'OrderByPriceDESC',
        'OrderByReleaseDateDESC',
        'OrderByTopSaleDESC'
      ]
    >;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface HrgComponentsHrgStoreWindowItem extends Schema.Component {
  collectionName: 'c_hrg_store_window_items';
  info: {
    displayName: 'HRG Store Window Item';
  };
  attributes: {
    filter_category_or_cluster: Attribute.String;
    media: Attribute.Media;
    navigate_to: Attribute.String;
  };
}

export interface ListLinksListLinksIconsItem extends Schema.Component {
  collectionName: 'c_list_links_icons_items';
  info: {
    displayName: 'List Links Icons item';
    icon: 'bulletList';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    navigate_to: Attribute.String;
    show_new_tag: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<false>;
    icon: Attribute.Media;
  };
}

export interface ListLinksListLinksIcons extends Schema.Component {
  collectionName: 'c_list_links_icons';
  info: {
    displayName: 'List Links Icons';
    icon: 'arrowRight';
    description: '';
  };
  attributes: {
    items: Attribute.Component<'list-links.list-links-icons-item', true> &
      Attribute.Required;
  };
}

export interface ListLinksListLinksItem extends Schema.Component {
  collectionName: 'c_list_links_items';
  info: {
    displayName: 'List Links item';
    icon: 'link';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    url: Attribute.String;
    navigate_to: Attribute.Text;
  };
}

export interface ListLinksListLinks extends Schema.Component {
  collectionName: 'c_list_links';
  info: {
    displayName: 'list links';
    icon: 'link';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    items: Attribute.Component<'list-links.list-links-item', true> &
      Attribute.Required;
  };
}

export interface MediaKitBannerListMediaKitBannerListItemNv
  extends Schema.Component {
  collectionName: 'c_media_kit_banner_list_items_nv';
  info: {
    displayName: 'Media Kit Banner List Item';
    icon: 'picture';
  };
  attributes: {
    image: Attribute.Media & Attribute.Required;
    navigate_to: Attribute.Text & Attribute.Required;
  };
}

export interface MediaKitBannerListMediaKitBannerListNv
  extends Schema.Component {
  collectionName: 'c_media_kit_banner_lists_nv';
  info: {
    displayName: 'Media Kit Banner List';
    icon: 'stack';
    description: '';
  };
  attributes: {
    position: Attribute.Integer & Attribute.Required;
    items: Attribute.Component<
      'media-kit-banner-list.media-kit-banner-list-item-nv',
      true
    > &
      Attribute.Required;
  };
}

export interface MediaKitBannerMediaKitBannerNv extends Schema.Component {
  collectionName: 'c_media_kit_banners_nv';
  info: {
    displayName: 'Media Kit Banner';
    icon: 'picture';
    description: '';
  };
  attributes: {
    position: Attribute.Integer & Attribute.Required;
    image: Attribute.Media & Attribute.Required;
    navigate_to: Attribute.Text & Attribute.Required;
  };
}

export interface MediaKitBannerMediaKitBanner extends Schema.Component {
  collectionName: 'c_media_kit_banners';
  info: {
    displayName: 'Media Kit Banner';
    icon: 'picture';
    description: '';
  };
  attributes: {
    show_before_product_at: Attribute.Integer & Attribute.Required;
    size: Attribute.Integer & Attribute.Required;
    image: Attribute.Media & Attribute.Required;
    navigate_to: Attribute.Text;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface MediaKitCategoriesMosaicMediaKitCategoriesMosaicItemNv
  extends Schema.Component {
  collectionName: 'c_media_kit_categories_mosaic_items_nv';
  info: {
    displayName: 'Media Kit Categories Mosaic Item';
    icon: 'picture';
  };
  attributes: {
    image: Attribute.Media & Attribute.Required;
    navigate_to: Attribute.Text & Attribute.Required;
  };
}

export interface MediaKitCategoriesMosaicMediaKitCategoriesMosaicNv
  extends Schema.Component {
  collectionName: 'c_media_kit_categories_mosaics_nv';
  info: {
    displayName: 'Media Kit Categories Mosaic';
    icon: 'grid';
    description: '';
  };
  attributes: {
    position: Attribute.Integer & Attribute.Required;
    title: Attribute.String;
    subtitle: Attribute.String;
    call_to_action: Attribute.String;
    cta_filter_category_or_cluster: Attribute.Text;
    items: Attribute.Component<
      'media-kit-categories-mosaic.media-kit-categories-mosaic-item-nv',
      true
    >;
  };
}

export interface MediaKitFullLookMediaKitFullLookBanner
  extends Schema.Component {
  collectionName: 'c_media_kit_full_look_media_kit_full_look_banners';
  info: {
    displayName: 'Media Kit Full Look Banner';
    icon: 'picture';
    description: '';
  };
  attributes: {
    products_ids: Attribute.String & Attribute.Required;
    media: Attribute.Media & Attribute.Required;
    show_before_product_at: Attribute.Integer & Attribute.Required;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface MediaKitProductCardMediaKitProductCard
  extends Schema.Component {
  collectionName: 'c_media_kit_product_cards';
  info: {
    displayName: 'media-kit-product-card';
    icon: 'command';
    description: '';
  };
  attributes: {
    product_ids: Attribute.String;
    media: Attribute.Media & Attribute.Required;
    show_before_product_at: Attribute.Integer & Attribute.Required;
    cta_text: Attribute.String;
    size: Attribute.Integer & Attribute.Required;
    navigate_to: Attribute.String;
    text_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    background_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    component_background_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface MediaListMediaListItem extends Schema.Component {
  collectionName: 'c_media_list_items';
  info: {
    displayName: 'Media List Item';
    icon: 'bulletList';
    description: '';
  };
  attributes: {
    video_background: Attribute.String;
    image_background: Attribute.Media;
    navigate_to: Attribute.String;
    title: Attribute.String;
    call_to_action: Attribute.String;
    header_color: Attribute.Enumeration<['claro', 'escuro']>;
    button_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    text_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface MediaListMediaList extends Schema.Component {
  collectionName: 'c_media_lists';
  info: {
    displayName: 'Media List';
    icon: 'stack';
    description: '';
  };
  attributes: {
    media: Attribute.Component<'media-list.media-list-item', true>;
  };
}

export interface MenuCollectionsMenuCollections extends Schema.Component {
  collectionName: 'c_menu_collections';
  info: {
    displayName: 'Menu Collections';
    icon: 'command';
    description: '';
  };
  attributes: {
    collections: Attribute.Component<'trend.menu-collections', true>;
  };
}

export interface MenuTabMenuTabAnimale extends Schema.Component {
  collectionName: 'c_menu_tab_animales';
  info: {
    displayName: 'Menu tab';
    icon: 'bulletList';
    description: '';
  };
  attributes: {
    is_active: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<true>;
    title: Attribute.String & Attribute.Required;
    title_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    content: Attribute.Relation<
      'menu-tab.menu-tab-animale',
      'oneToOne',
      'api::menu-contents-animale.menu-contents-animale'
    >;
  };
}

export interface MenuTabMenuTabCrisbarros extends Schema.Component {
  collectionName: 'c_menu_tab_crisbarros';
  info: {
    displayName: 'Menu tab';
    icon: 'bulletList';
    description: '';
  };
  attributes: {
    is_active: Attribute.Boolean & Attribute.Required;
    title: Attribute.String & Attribute.Required;
    title_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    content: Attribute.Relation<
      'menu-tab.menu-tab-crisbarros',
      'oneToOne',
      'api::menu-contents-crisbarros.menu-contents-crisbarros'
    >;
  };
}

export interface MenuTabMenuTabFarmEtc extends Schema.Component {
  collectionName: 'c_menu_tab_farm_etcs';
  info: {
    displayName: 'Menu tab ETC';
    icon: 'arrowRight';
    description: '';
  };
  attributes: {
    is_active: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<true>;
    title: Attribute.String & Attribute.Required;
    title_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    content: Attribute.Relation<
      'menu-tab.menu-tab-farm-etc',
      'oneToOne',
      'api::menu-contents-farm-etc.menu-contents-farm-etc'
    >;
    show_new_tag: Attribute.Boolean & Attribute.DefaultTo<false>;
    tag: Attribute.Component<'accordion-menu.accordion-menu-tag'>;
    badge_count: Attribute.Integer;
  };
}

export interface MenuTabMenuTabFarmNew extends Schema.Component {
  collectionName: 'c_menu_tab_farm_news';
  info: {
    displayName: 'menu tab farm new';
    icon: 'arrowRight';
    description: '';
  };
  attributes: {
    is_active: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<true>;
    title: Attribute.String & Attribute.Required;
    title_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    content: Attribute.Relation<
      'menu-tab.menu-tab-farm-new',
      'oneToOne',
      'api::menu-contents-farm.menu-contents-farm'
    >;
    show_new_tag: Attribute.Boolean;
    tag: Attribute.Component<'accordion-menu.accordion-menu-tag'>;
    header_image: Attribute.Media;
    badge_count: Attribute.Integer;
  };
}

export interface MenuTabMenuTabFarm extends Schema.Component {
  collectionName: 'c_menu_tab_farms';
  info: {
    displayName: 'Menu tab';
    icon: 'bulletList';
    description: '';
  };
  attributes: {
    is_active: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<true>;
    title: Attribute.String & Attribute.Required;
    title_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    content: Attribute.Relation<
      'menu-tab.menu-tab-farm',
      'oneToOne',
      'api::menu-contents-farm.menu-contents-farm'
    >;
  };
}

export interface MenuTabMenuTabHering extends Schema.Component {
  collectionName: 'c_menu_tab_herings';
  info: {
    displayName: 'Menu tab';
    icon: 'bulletList';
    description: '';
  };
  attributes: {
    is_active: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<true>;
    title: Attribute.String & Attribute.Required;
    title_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    content: Attribute.Relation<
      'menu-tab.menu-tab-hering',
      'oneToOne',
      'api::menu-contents-hering.menu-contents-hering'
    >;
  };
}

export interface MenuTabMenuTabMariafilo extends Schema.Component {
  collectionName: 'c_menu_tab_mariafilos';
  info: {
    displayName: 'Menu tab';
    icon: 'bulletList';
    description: '';
  };
  attributes: {
    is_active: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<true>;
    title: Attribute.String & Attribute.Required;
    title_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    content: Attribute.Relation<
      'menu-tab.menu-tab-mariafilo',
      'oneToOne',
      'api::menu-contents-mariafilo.menu-contents-mariafilo'
    >;
  };
}

export interface MenuTabMenuTabNv extends Schema.Component {
  collectionName: 'c_menu_tab_nvs';
  info: {
    displayName: 'Menu tab';
    icon: 'bulletList';
    description: '';
  };
  attributes: {
    is_active: Attribute.Boolean;
    title_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    title: Attribute.String & Attribute.Required;
    content: Attribute.Relation<
      'menu-tab.menu-tab-nv',
      'oneToOne',
      'api::menu-contents-nv.menu-contents-nv'
    >;
  };
}

export interface MenuTabMenuTabOffpremium extends Schema.Component {
  collectionName: 'c_menu_tab_offpremiums';
  info: {
    displayName: 'Menu tab';
    icon: 'bulletList';
    description: '';
  };
  attributes: {
    is_active: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<true>;
    title: Attribute.String & Attribute.Required;
    title_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    content: Attribute.Relation<
      'menu-tab.menu-tab-offpremium',
      'oneToOne',
      'api::menu-contents-offpremium.menu-contents-offpremium'
    >;
  };
}

export interface MosaicCategoriesMosaic extends Schema.Component {
  collectionName: 'c_categories_mosaics_nv';
  info: {
    displayName: 'Categories Mosaic';
    icon: 'apps';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    subtitle: Attribute.String;
    call_to_action: Attribute.String;
    cta_filter_category_cluster: Attribute.String & Attribute.Required;
    items: Attribute.Component<'mosaic.category-mosaic-item', true>;
  };
}

export interface MosaicCategoryMosaicItem extends Schema.Component {
  collectionName: 'c_category_mosaic_items';
  info: {
    displayName: 'Category Mosaic Item';
    icon: 'bulletList';
    description: '';
  };
  attributes: {
    filter_category_or_cluster: Attribute.String & Attribute.Required;
    filter_color: Attribute.String;
    filter_size: Attribute.String;
    search_key_words: Attribute.String;
    search_id: Attribute.String;
    image: Attribute.Media & Attribute.Required;
    order_by_intelligent_search: Attribute.Enumeration<
      [
        'OrderByBestDiscountDESC',
        'OrderByPriceASC',
        'OrderByPriceDESC',
        'OrderByReleaseDateDESC',
        'OrderByTopSaleDESC'
      ]
    >;
    filtre_type: Attribute.Enumeration<['category', 'collection']> &
      Attribute.Required;
  };
}

export interface MosaicProductsMosaic extends Schema.Component {
  collectionName: 'c_products_mosaics';
  info: {
    displayName: 'Products Mosaic';
    icon: 'apps';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    subtitle: Attribute.String;
    call_to_action: Attribute.String & Attribute.Required;
    filter_category_or_cluster: Attribute.String & Attribute.Required;
    title_image: Attribute.Media;
  };
}

export interface MyInterestsBannerMyInteressesWithHeader
  extends Schema.Component {
  collectionName: 'c_my_interesses_with_headers';
  info: {
    displayName: 'my interesets with header';
    icon: 'alien';
    description: '';
  };
  attributes: {
    interest: Attribute.Component<'my-interests-banner.my-interests-banner'> &
      Attribute.Required;
    header_color: Attribute.Enumeration<['claro', 'escuro']> &
      Attribute.Required;
  };
}

export interface MyInterestsBannerMyInterestsBanner extends Schema.Component {
  collectionName: 'c_my_interests_banners';
  info: {
    displayName: 'My Interests Banner';
    icon: 'picture';
  };
  attributes: {
    title: Attribute.String;
    subtitle: Attribute.Text;
    call_to_action: Attribute.String;
    image_interests: Attribute.Media & Attribute.Required;
    image_no_interests: Attribute.Media & Attribute.Required;
  };
}

export interface MyInterestsCardMyInterestsCard extends Schema.Component {
  collectionName: 'c_my_interests_card';
  info: {
    displayName: 'My Interests Card';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    subtitle: Attribute.Text & Attribute.Required;
    call_to_action_with_interests: Attribute.String & Attribute.Required;
    icon: Attribute.Media & Attribute.Required;
    call_to_action_without_interests: Attribute.String & Attribute.Required;
  };
}

export interface NotificationCenterExpandedNotificationMessage
  extends Schema.Component {
  collectionName: 'c_expanded_messages';
  info: {
    displayName: 'Expanded Notification Message';
    icon: 'bell';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    text_content: Attribute.Component<'text-content.text-content', true>;
    call_to_action: Attribute.String & Attribute.Required;
    navigate_to: Attribute.String & Attribute.Required;
    coupon: Attribute.String;
    message: Attribute.String;
    notification_banner: Attribute.Media;
  };
}

export interface NotificationCenterMessageBanner extends Schema.Component {
  collectionName: 'c_message_banners';
  info: {
    displayName: 'Message banner';
    icon: 'bell';
    description: '';
  };
  attributes: {
    notification_banner: Attribute.Media;
    icon: Attribute.Media;
    title: Attribute.String & Attribute.Required;
    message: Attribute.Text & Attribute.Required;
    navigate_to: Attribute.String;
  };
}

export interface NotificationCenterMessage extends Schema.Component {
  collectionName: 'c_messages';
  info: {
    displayName: 'message';
    icon: 'bell';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    message: Attribute.Text & Attribute.Required;
    navigate_to: Attribute.String;
    icon: Attribute.Media;
    call_to_action: Attribute.String;
  };
}

export interface NotificationCenterPageData extends Schema.Component {
  collectionName: 'c_page_datas';
  info: {
    displayName: 'page data';
    icon: 'feather';
    description: '';
  };
  attributes: {
    alert_text_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    alert_background_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    client_classification: Attribute.Enumeration<
      ['Todos', 'BLACK', 'GOLD', 'INATIVO', 'PERDIDO', 'PLATINUM', 'REGULAR']
    > &
      Attribute.Required;
    notification_redirect_type: Attribute.Enumeration<
      [
        'Navega\u00E7\u00E3o din\u00E2mica',
        'Navegar para Central de Notifica\u00E7\u00F5es'
      ]
    > &
      Attribute.DefaultTo<'Navega\u00E7\u00E3o din\u00E2mica'>;
    alpha: Attribute.Decimal &
      Attribute.SetMinMax<
        {
          min: 0;
          max: 1;
        },
        number
      > &
      Attribute.DefaultTo<0.5>;
  };
}

export interface NotificationCenterRedirectSection extends Schema.Component {
  collectionName: 'c_redirect_sections';
  info: {
    displayName: 'Redirect section';
    icon: 'arrowRight';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    subtitle: Attribute.Text & Attribute.Required;
    call_to_action: Attribute.String & Attribute.Required;
    category_filter: Attribute.String & Attribute.Required;
  };
}

export interface NotificationStepNotificationStep extends Schema.Component {
  collectionName: 'c_step_notification_steps';
  info: {
    displayName: 'Notification Step';
    icon: 'bell';
    description: '';
  };
  attributes: {
    request_notification_permission: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<true>;
    title: Attribute.String & Attribute.Required;
    description: Attribute.String & Attribute.Required;
    background_image: Attribute.Media & Attribute.Required;
    complement_image: Attribute.Media;
    is_slide_image: Attribute.Boolean & Attribute.DefaultTo<false>;
  };
}

export interface OrderDetailsOrderDetailsWarningText extends Schema.Component {
  collectionName: 'c_order_details_warning_texts';
  info: {
    displayName: 'Order Details Warning Text';
    icon: 'information';
    description: '';
  };
  attributes: {
    text: Attribute.String & Attribute.Required;
    is_active: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<false>;
  };
}

export interface PackagingPackaging extends Schema.Component {
  collectionName: 'c_packagings';
  info: {
    displayName: 'packaging';
    icon: 'briefcase';
    description: '';
  };
  attributes: {
    image: Attribute.Media & Attribute.Required;
    title: Attribute.String & Attribute.Required;
    subtitle: Attribute.Text & Attribute.Required;
  };
}

export interface PaymentWarningPaymentWarning extends Schema.Component {
  collectionName: 'c_payment_warning_payment_warnings';
  info: {
    displayName: 'Payment Warning';
    description: 'Componente para ser exibido na tela de selecao de pagamento no checkout';
  };
  attributes: {
    warningText: Attribute.String;
    warningType: Attribute.Enumeration<['Neutral', 'Success', 'Alert']>;
  };
}

export interface PdcHeaderEtcPdcHeaderEtc extends Schema.Component {
  collectionName: 'c_pdc_header_etc_pdc_header_etcs';
  info: {
    displayName: 'PDC Header ETC';
    icon: 'picture';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    subtitle: Attribute.String;
    medias: Attribute.Media;
    logo: Attribute.Media;
  };
}

export interface PersonalShowcasePersonalShowcase extends Schema.Component {
  collectionName: 'components_personal_showcase_personal_showcases';
  info: {
    displayName: 'Personal-Showcase';
    icon: 'command';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    subtitle: Attribute.String;
    call_to_action: Attribute.String;
    filter_category_or_cluster: Attribute.String;
    showcase_type: Attribute.Enumeration<
      [
        'LastSeenProducts',
        'WishlistProducts',
        'MyInterests',
        'LastSeenProductsRecommendations',
        'LastPurchasedCategoryProducts',
        'NewReleases'
      ]
    > &
      Attribute.Required;
    order_by: Attribute.Enumeration<
      [
        'OrderByBestDiscountDESC',
        'OrderByPriceASC',
        'OrderByPriceDESC',
        'OrderByReleaseDateDESC',
        'OrderByTopSaleDESC'
      ]
    >;
    include_user_name: Attribute.Boolean;
  };
}

export interface ProductCarouselImageProductCarouselImage
  extends Schema.Component {
  collectionName: 'c_product_carousel_images';
  info: {
    displayName: 'Product Carousel Image';
    icon: 'landscape';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    subtitle: Attribute.Text & Attribute.Required;
    filter_category_or_cluster: Attribute.String & Attribute.Required;
    call_to_action: Attribute.String;
    orderby: Attribute.Enumeration<
      [
        'OrderByBestDiscountDESC',
        'OrderByPriceASC',
        'OrderByPriceDESC',
        'OrderByReleaseDateDESC',
        'OrderByTopSaleDESC'
      ]
    >;
    text_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    background_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    image: Attribute.Media & Attribute.Required;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface ProductStampSportStampCollection extends Schema.Component {
  collectionName: 'c_product_stamp_sport_technology_collection';
  info: {
    displayName: 'Sport Stamp Collection';
    description: '';
  };
  attributes: {
    stamp_collection_title: Attribute.String;
    stamps: Attribute.Component<'product-stamp.sport-technology-stamp', true>;
  };
}

export interface ProductStampSportTechnologyStamp extends Schema.Component {
  collectionName: 'c_product_stamp_sport_technology_stamp';
  info: {
    displayName: 'Sport Technology Stamp';
    description: '';
  };
  attributes: {
    icon: Attribute.Media;
    title: Attribute.String;
    description: Attribute.String;
    filter_category_or_cluster: Attribute.String;
  };
}

export interface ProductStampStamp extends Schema.Component {
  collectionName: 'c_stamp_stamps';
  info: {
    displayName: 'Stamp';
    icon: 'write';
    description: '';
  };
  attributes: {
    icon: Attribute.Media & Attribute.Required;
    filter_category_or_cluster: Attribute.String & Attribute.Required;
  };
}

export interface PromotionPromotionCard extends Schema.Component {
  collectionName: 'components_promotion_promotion_cards';
  info: {
    displayName: 'Promotion Card';
    icon: 'server';
    description: '';
  };
  attributes: {
    is_active: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<true>;
    promotion_id: Attribute.String;
    title: Attribute.Text;
    subtitle: Attribute.Text;
    feedback_text: Attribute.Text;
    show_progress_bar: Attribute.Boolean;
    button_text: Attribute.String;
    button_navigate_to: Attribute.Text;
    feedback_subtitle: Attribute.Text;
    icon: Attribute.Media;
    theme: Attribute.Enumeration<['claro', 'escuro']> &
      Attribute.DefaultTo<'claro'>;
    delivery_type: Attribute.Enumeration<
      ['Entrega Normal', 'Entrega via loja']
    >;
  };
}

export interface QuickFilterQuickFilterComponent extends Schema.Component {
  collectionName: 'c_quick_filter_quick_filter_components';
  info: {
    displayName: 'Quick Filter';
    icon: 'oneToMany';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    filter_category_or_cluster: Attribute.String & Attribute.Required;
    image: Attribute.Media & Attribute.Required;
  };
}

export interface SearchStepSearchStep extends Schema.Component {
  collectionName: 'c_search_steps';
  info: {
    displayName: 'Search Step';
    icon: 'search';
    description: '';
  };
  attributes: {
    search_example: Attribute.String & Attribute.Required;
    title: Attribute.String & Attribute.Required;
    description: Attribute.String & Attribute.Required;
    background_image: Attribute.Media & Attribute.Required;
    search_category: Attribute.Component<'text-field.text-field', true>;
  };
}

export interface SearchTrendSearchTrend extends Schema.Component {
  collectionName: 'c_search_trend_search_trends';
  info: {
    displayName: 'Search Trend';
    icon: 'search';
    description: '';
  };
  attributes: {
    search_trend: Attribute.Component<'trend.search-trend', true>;
  };
}

export interface ShowcaseBannerListShowCaseBannerCarrousel
  extends Schema.Component {
  collectionName: 'c_show_case_banner_carrousels';
  info: {
    displayName: 'ShowCase Banner Carrousel';
    icon: 'bulletList';
    description: '';
  };
  attributes: {
    items: Attribute.Component<
      'showcase-banner-list.showcase-banner-item',
      true
    > &
      Attribute.Required;
  };
}

export interface ShowcaseBannerListShowcaseBannerItem extends Schema.Component {
  collectionName: 'c_showcase_banner_items';
  info: {
    displayName: 'ShowCase Banner item';
    icon: 'archive';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    banner: Attribute.Media & Attribute.Required;
    call_to_action: Attribute.String;
    navigate_to: Attribute.String & Attribute.Required;
    text_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface ShowcaseBannerShowcaseBanner extends Schema.Component {
  collectionName: 'c_showcase_banners';
  info: {
    displayName: 'Showcase Banner';
    icon: 'command';
    description: '';
  };
  attributes: {
    image_background: Attribute.Media & Attribute.Required;
    call_to_action: Attribute.String & Attribute.Required;
    filter_category_or_cluster: Attribute.String & Attribute.Required;
    filter_size: Attribute.String;
    query: Attribute.String;
    header_color: Attribute.Enumeration<['claro', 'escuro']> &
      Attribute.DefaultTo<'claro'>;
    order_by: Attribute.Enumeration<
      [
        'OrderByBestDiscountDESC',
        'OrderByPriceASC',
        'OrderByPriceDESC',
        'OrderByReleaseDateDESC',
        'OrderByTopSaleDESC'
      ]
    >;
  };
}

export interface ShowcaseListShowCaseListItem extends Schema.Component {
  collectionName: 'c_show_case_list_items';
  info: {
    displayName: 'ShowCase List Item';
    icon: 'landscape';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    filter_category_or_cluster: Attribute.String & Attribute.Required;
    order_by_intelligent_search: Attribute.Enumeration<
      [
        'OrderByBestDiscountDESC',
        'OrderByPriceASC',
        'OrderByPriceDESC',
        'OrderByReleaseDateDESC',
        'OrderByTopSaleDESC'
      ]
    >;
  };
}

export interface ShowcaseListShowCaseList extends Schema.Component {
  collectionName: 'c_list_show_case_lists';
  info: {
    displayName: 'ShowCase List';
    icon: 'landscape';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    subtitle: Attribute.String;
    call_to_action: Attribute.String;
    filter_category_or_cluster: Attribute.String & Attribute.Required;
    order_by_intelligent_search: Attribute.Enumeration<
      [
        'OrderByBestDiscountDESC',
        'OrderByPriceASC',
        'OrderByPriceDESC',
        'OrderByReleaseDateDESC',
        'OrderByTopSaleDESC'
      ]
    >;
    items: Attribute.Component<'showcase-list.show-case-list-item', true>;
  };
}

export interface SignUpLoginSignUpLogin extends Schema.Component {
  collectionName: 'c_sign_up_logins';
  info: {
    displayName: 'Sign Up Login';
    icon: 'user';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    description: Attribute.Text & Attribute.Required;
    image: Attribute.Media;
    buttons_direction: Attribute.String &
      Attribute.Required &
      Attribute.DefaultTo<'column'>;
  };
}

export interface SmartRecommendationInsiderRecommendedShowcase
  extends Schema.Component {
  collectionName: 'c_insider_recommended_showcases';
  info: {
    displayName: 'InsiderRecommendedShowcase';
    icon: 'bulletList';
    description: '';
  };
  attributes: {
    campaign_id: Attribute.Integer & Attribute.Required;
    title: Attribute.String;
    layout: Attribute.Enumeration<['list', 'grid']> &
      Attribute.DefaultTo<'list'>;
    algorithm_type: Attribute.Enumeration<['generic', 'product_based']> &
      Attribute.DefaultTo<'generic'>;
    position: Attribute.Enumeration<
      ['Fim da p\u00E1gina', "Antes de 'Sobre a pe\u00E7a'"]
    > &
      Attribute.Required;
  };
}

export interface SpotProductClockSpotProductClock extends Schema.Component {
  collectionName: 'c_spot_product_clocks';
  info: {
    displayName: 'Spot Product Clock';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    subtitle: Attribute.Text;
    call_to_action: Attribute.String;
    coupon: Attribute.String;
    filter_category_or_cluster: Attribute.String & Attribute.Required;
    orderby: Attribute.Enumeration<
      [
        'OrderByBestDiscountDESC',
        'OrderByPriceASC',
        'OrderByPriceDESC',
        'OrderByReleaseDateDESC',
        'OrderByTopSaleDESC'
      ]
    >;
    start_time: Attribute.DateTime & Attribute.Required;
    end_time: Attribute.DateTime & Attribute.Required;
    text_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    background_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    rules_description: Attribute.Text;
    rules_button_text: Attribute.String;
  };
}

export interface SpotProductSpotProduct extends Schema.Component {
  collectionName: 'c_spot_products';
  info: {
    displayName: 'Spot-Product';
    icon: 'cloud';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    subtitle: Attribute.Text & Attribute.Required;
    call_to_action: Attribute.String;
    orderBy: Attribute.Enumeration<
      [
        'OrderByBestDiscountDESC',
        'OrderByPriceASC',
        'OrderByPriceDESC',
        'OrderByReleaseDateDESC',
        'OrderByTopSaleDESC'
      ]
    >;
    filter_category_or_cluster: Attribute.String & Attribute.Required;
    filter_color: Attribute.String;
    filter_size: Attribute.String;
    query: Attribute.String;
    search_title: Attribute.String;
  };
}

export interface StepStep extends Schema.Component {
  collectionName: 'c_steps';
  info: {
    displayName: 'step';
    icon: 'picture';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    description: Attribute.String & Attribute.Required;
    background_image: Attribute.Media & Attribute.Required;
    complement_image: Attribute.Media;
    is_slide_image: Attribute.Boolean & Attribute.DefaultTo<false>;
    request_localization_permission: Attribute.Boolean &
      Attribute.DefaultTo<false>;
  };
}

export interface StoriesItems extends Schema.Component {
  collectionName: 'c_stories_items';
  info: {
    displayName: 'Story Item';
    icon: 'landscape';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    media: Attribute.Media & Attribute.Required;
    navigate_to: Attribute.Text & Attribute.Required;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface StoriesStories extends Schema.Component {
  collectionName: 'c_stories';
  info: {
    displayName: 'Stories';
    icon: 'landscape';
    description: '';
  };
  attributes: {
    items: Attribute.Component<'stories.items', true>;
    title: Attribute.String;
  };
}

export interface SuggestionsCards extends Schema.Component {
  collectionName: 'c_suggestions_cards';
  info: {
    displayName: 'Cards';
    icon: 'archive';
  };
  attributes: {
    Cards: Attribute.Component<'suggestions.suggestions-card', true>;
  };
}

export interface SuggestionsSuggestionsCard extends Schema.Component {
  collectionName: 'c_suggestions_suggestions_cards';
  info: {
    displayName: 'suggestions card';
    icon: 'command';
    description: '';
  };
  attributes: {
    image: Attribute.Media & Attribute.Required;
    title: Attribute.String & Attribute.Required;
    subtitle: Attribute.String & Attribute.Required;
    product_list: Attribute.String & Attribute.Required;
    description: Attribute.Text & Attribute.Required;
    video_thumb: Attribute.Media;
    description_video: Attribute.Text;
    video_url: Attribute.String;
  };
}

export interface SuggestionsSuggestionsTitle extends Schema.Component {
  collectionName: 'c_suggestions_suggestions_titles';
  info: {
    displayName: 'suggestions title';
    icon: 'command';
    description: '';
  };
  attributes: {
    title: Attribute.Text & Attribute.Required;
  };
}

export interface SuitcaseSuggestionSuitcaseSuggestion extends Schema.Component {
  collectionName: 'components_suitcase_suggestion_suitcase_suggestions';
  info: {
    displayName: 'Suitcase-Suggestion';
    description: '';
  };
  attributes: {
    component_title: Attribute.String & Attribute.Required;
    component_subtitle: Attribute.String & Attribute.Required;
    id_list: Attribute.String & Attribute.Required;
    image: Attribute.Media & Attribute.Required;
  };
}

export interface TagsColors extends Schema.Component {
  collectionName: 'c_tags_colors';
  info: {
    displayName: 'colors';
    icon: 'sun';
  };
  attributes: {
    background_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    text_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    border_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
  };
}

export interface TagsFidelityTags extends Schema.Component {
  collectionName: 'c_tags_fidelity_tags';
  info: {
    displayName: 'Fidelity Tag';
    description: '';
    icon: 'database';
  };
  attributes: {
    filter_category_or_cluster: Attribute.String & Attribute.Required;
    orderBy: Attribute.Enumeration<
      [
        'OrderByBestDiscountDESC',
        'OrderByPriceASC',
        'OrderByPriceDESC',
        'OrderByReleaseDateDESC',
        'OrderByTopSaleDESC'
      ]
    >;
    colors: Attribute.Component<'tags.colors'> & Attribute.Required;
  };
}

export interface TagsPickupTag extends Schema.Component {
  collectionName: 'c_tags_pickup_tags';
  info: {
    displayName: 'Pickup Tag';
    icon: 'walk';
    description: '';
  };
  attributes: {
    colors: Attribute.Component<'tags.colors'> & Attribute.Required;
    title: Attribute.String & Attribute.Required;
    active: Attribute.Boolean & Attribute.Required;
  };
}

export interface TagsPixImmediateTag extends Schema.Component {
  collectionName: 'c_tags_pix_immediate_tags';
  info: {
    displayName: 'Pix Immediate Tag';
    description: '';
  };
  attributes: {
    colors: Attribute.Component<'tags.colors'> & Attribute.Required;
    title: Attribute.String &
      Attribute.Required &
      Attribute.DefaultTo<'titulo'>;
    active: Attribute.Boolean & Attribute.Required;
    discount_value: Attribute.Integer & Attribute.Required;
  };
}

export interface TagsPixTag extends Schema.Component {
  collectionName: 'c_tags_pix_tags';
  info: {
    displayName: 'Pix Tag';
    icon: 'strikeThrough';
    description: '';
  };
  attributes: {
    colors: Attribute.Component<'tags.colors'> & Attribute.Required;
    title: Attribute.String & Attribute.Required;
    active: Attribute.Boolean & Attribute.Required;
    discount_value: Attribute.Integer & Attribute.Required;
  };
}

export interface TagsProductColorTag extends Schema.Component {
  collectionName: 'c_tags_product_color_tags';
  info: {
    displayName: 'PDP Color Tag';
    icon: 'brush';
    description: '';
  };
  attributes: {
    filter_category_or_cluster: Attribute.String & Attribute.Required;
    tag_text: Attribute.String & Attribute.Required;
    tag_color_text: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
  };
}

export interface TagsProductTag extends Schema.Component {
  collectionName: 'c_product_tag_product_tags';
  info: {
    displayName: 'Product Tag';
    icon: 'bold';
    description: '';
  };
  attributes: {
    filter_category_or_cluster: Attribute.String & Attribute.Required;
    orderBy: Attribute.Enumeration<
      [
        'OrderByBestDiscountDESC',
        'OrderByPriceASC',
        'OrderByPriceDESC',
        'OrderByReleaseDateDESC',
        'OrderByTopSaleDESC'
      ]
    >;
    title: Attribute.String & Attribute.Required;
    colors: Attribute.Component<'tags.colors'> & Attribute.Required;
  };
}

export interface TagsStampTagProduct extends Schema.Component {
  collectionName: 'c_stamp_tag_products';
  info: {
    displayName: 'stamp-tag-product';
    icon: 'shirt';
    description: '';
  };
  attributes: {
    tag_text: Attribute.String & Attribute.Required;
    products_ids: Attribute.String & Attribute.Required;
    colors: Attribute.Component<'tags.colors'>;
  };
}

export interface TagsStampTag extends Schema.Component {
  collectionName: 'c_stamp_tags';
  info: {
    displayName: 'stamp-tag';
    icon: 'underline';
    description: '';
  };
  attributes: {
    tag_text: Attribute.String & Attribute.Required;
    filter_category_or_cluster: Attribute.String & Attribute.Required;
    colors: Attribute.Component<'tags.colors'>;
  };
}

export interface TextContentCallToAction extends Schema.Component {
  collectionName: 'c_call_to_actions';
  info: {
    displayName: 'Call To Action';
    icon: 'arrowRight';
  };
  attributes: {
    call_to_action: Attribute.String & Attribute.Required;
    navigate_to: Attribute.String & Attribute.Required;
  };
}

export interface TextContentListText extends Schema.Component {
  collectionName: 'c_list_texts';
  info: {
    displayName: 'List Text';
    icon: 'bulletList';
  };
  attributes: {
    text: Attribute.String;
  };
}

export interface TextContentTextContent extends Schema.Component {
  collectionName: 'c_text_contents';
  info: {
    displayName: 'Text content';
    icon: 'filter';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.Blocks;
  };
}

export interface TextContentTitleAndDescription extends Schema.Component {
  collectionName: 'c_title_and_descriptions';
  info: {
    displayName: 'Title and Description';
    icon: 'layer';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.String;
  };
}

export interface TextFieldTextField extends Schema.Component {
  collectionName: 'c_text_fields';
  info: {
    displayName: 'Text Field';
    icon: 'layer';
    description: '';
  };
  attributes: {
    text: Attribute.String & Attribute.Required;
  };
}

export interface TipBarAnimatedTipBar extends Schema.Component {
  collectionName: 'components_tip_bar_animated_tip_bars';
  info: {
    displayName: 'Animated TipBar';
    icon: 'filter';
    description: '';
  };
  attributes: {
    image: Attribute.Media & Attribute.Required;
    navigate_to: Attribute.Text;
    background_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    is_active: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<true>;
  };
}

export interface TipBarStaticTipBar extends Schema.Component {
  collectionName: 'c_static_tip_bars';
  info: {
    displayName: 'Static TipBar';
    icon: 'layer';
    description: '';
  };
  attributes: {
    text: Attribute.String & Attribute.Required;
    call_to_action: Attribute.String;
    text_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    background_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    navigate_to: Attribute.Text;
    is_active: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<true>;
  };
}

export interface TrackingStepTrackingStepDefault extends Schema.Component {
  collectionName: 'c_tracking_step_defaults';
  info: {
    displayName: 'tracking-step-default';
    icon: 'connector';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    description: Attribute.String & Attribute.Required;
    background_image: Attribute.Media & Attribute.Required;
    request_tracking: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<true>;
  };
}

export interface TrackingStepTrackingStep extends Schema.Component {
  collectionName: 'components_tracking_sep_tracking_steps';
  info: {
    displayName: 'Tracking Step (Apenas iOS)';
    icon: 'cloud';
    description: '';
  };
  attributes: {
    background_color: Attribute.String &
      Attribute.Required &
      Attribute.CustomField<'plugin::color-picker.color'>;
    text_content: Attribute.Component<'text-content.text-content', true> &
      Attribute.Required;
  };
}

export interface TrendMenuCollections extends Schema.Component {
  collectionName: 'components_trend_menu_collections';
  info: {
    displayName: 'menu_collections';
    icon: 'command';
    description: '';
  };
  attributes: {
    call_to_action: Attribute.String & Attribute.Required;
    media: Attribute.Media;
    navigate_to: Attribute.String & Attribute.Required;
  };
}

export interface TrendSearchTrend extends Schema.Component {
  collectionName: 'components_trend_search_trends';
  info: {
    displayName: 'search_trend';
  };
  attributes: {
    Trend: Attribute.String & Attribute.Required;
  };
}

export interface VideoBannerVideoBanner extends Schema.Component {
  collectionName: 'c_banner_video_banners';
  info: {
    displayName: 'Video Banner';
    icon: 'play';
    description: '';
  };
  attributes: {
    background_video_url: Attribute.String & Attribute.Required;
    title: Attribute.String;
    call_to_action: Attribute.String;
    filter_category_or_cluster: Attribute.String & Attribute.Required;
    header_color: Attribute.Enumeration<['claro', 'escuro']>;
    text_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    button_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    banner_name_ga4: Attribute.String & Attribute.Required;
  };
}

export interface VideoGalleryVideoGalleryItem extends Schema.Component {
  collectionName: 'c_video_gallery_items';
  info: {
    displayName: 'Video Gallery Item';
    description: '';
  };
  attributes: {
    image: Attribute.Media & Attribute.Required;
    video_url: Attribute.String & Attribute.Required;
    title: Attribute.String;
    description: Attribute.String;
    navigate_to: Attribute.Text;
    banner_name_ga4: Attribute.String;
  };
}

export interface VideoGalleryVideoGallery extends Schema.Component {
  collectionName: 'c_video_galleries';
  info: {
    displayName: 'Video Gallery';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    subtitle: Attribute.String;
    items: Attribute.Component<'video-gallery.video-gallery-item', true>;
  };
}

declare module '@strapi/types' {
  export module Shared {
    export interface Components {
      'accordion-menu.accordion-menu-etc-item': AccordionMenuAccordionMenuEtcItem;
      'accordion-menu.accordion-menu-etc': AccordionMenuAccordionMenuEtc;
      'accordion-menu.accordion-menu-item': AccordionMenuAccordionMenuItem;
      'accordion-menu.accordion-menu-tag': AccordionMenuAccordionMenuTag;
      'accordion-menu.accordion-menu': AccordionMenuAccordionMenu;
      'accordion-menu.content-media-menu': AccordionMenuContentMediaMenu;
      'ai-assistant.ai-asking-suggestion': AiAssistantAiAskingSuggestion;
      'badge-discount.badge-discount': BadgeDiscountBadgeDiscount;
      'banner-content-text.banner-content-text-double-media': BannerContentTextBannerContentTextDoubleMedia;
      'banner-content-text.banner-content-text-media-item': BannerContentTextBannerContentTextMediaItem;
      'banner-content-text.banner-content-text': BannerContentTextBannerContentText;
      'banner-media.banner-media': BannerMediaBannerMedia;
      'base-content.filter-list': BaseContentFilterList;
      'base-content.title-description': BaseContentTitleDescription;
      'brand-slogan-lockup.brand-slogan': BrandSloganLockupBrandSlogan;
      'call-center.call-center-card': CallCenterCallCenterCard;
      'call-center.call-center-faq': CallCenterCallCenterFaq;
      'call-center.cards': CallCenterCards;
      'call-center.faq': CallCenterFaq;
      'carousel-banner.carousel-banner-item': CarouselBannerCarouselBannerItem;
      'carousel-banner.carousel-banner': CarouselBannerCarouselBanner;
      'carousel-list.carousel-list-button': CarouselListCarouselListButton;
      'carousel-list.carousel-list-item': CarouselListCarouselListItem;
      'carousel-list.carousel-list': CarouselListCarouselList;
      'carousel-list.double-media-item': CarouselListDoubleMediaItem;
      'cashback.message-cashback-pdp': CashbackMessageCashbackPdp;
      'cashback.message-cashback': CashbackMessageCashback;
      'categories-mosaic.categories-mosaic-item': CategoriesMosaicCategoriesMosaicItem;
      'categories-mosaic.categories-mosaic': CategoriesMosaicCategoriesMosaic;
      'category-banner.category-banner': CategoryBannerCategoryBanner;
      'clock-promotion.clock-banner-animale': ClockPromotionClockBannerAnimale;
      'clock-promotion.clock-banner-nv': ClockPromotionClockBannerNv;
      'clock-promotion.clock-header-animale': ClockPromotionClockHeaderAnimale;
      'clock-promotion.clock-header-crisbarros': ClockPromotionClockHeaderCrisbarros;
      'clock-promotion.clock-header-farm': ClockPromotionClockHeaderFarm;
      'clock-promotion.clock-header-hering': ClockPromotionClockHeaderHering;
      'clock-promotion.clock-header-maria-filo': ClockPromotionClockHeaderMariaFilo;
      'clock-promotion.clock-header-nv': ClockPromotionClockHeaderNv;
      'clock-promotion.clock-header-off': ClockPromotionClockHeaderOff;
      'clock-promotion.clock-pages-animale': ClockPromotionClockPagesAnimale;
      'clock-promotion.clock-pages-crisbarros': ClockPromotionClockPagesCrisbarros;
      'clock-promotion.clock-pages-farm': ClockPromotionClockPagesFarm;
      'clock-promotion.clock-pages-hering': ClockPromotionClockPagesHering;
      'clock-promotion.clock-pages-maria-filo': ClockPromotionClockPagesMariaFilo;
      'clock-promotion.clock-pages-nv': ClockPromotionClockPagesNv;
      'clock-promotion.clock-pages-off': ClockPromotionClockPagesOff;
      'clock-promotion.clock-product-farm': ClockPromotionClockProductFarm;
      'clock-promotion.clock-product-hering': ClockPromotionClockProductHering;
      'clock.clock-animale': ClockClockAnimale;
      'clock.clock-crisbarros': ClockClockCrisbarros;
      'clock.clock-farm': ClockClockFarm;
      'clock.clock-hering': ClockClockHering;
      'clock.clock-maria-filo': ClockClockMariaFilo;
      'clock.clock-nv': ClockClockNv;
      'clock.clock-off': ClockClockOff;
      'clusters-components.category-banner-cluster': ClustersComponentsCategoryBannerCluster;
      'clusters-components.coupon-banner-cluster': ClustersComponentsCouponBannerCluster;
      'clusters-components.general-clock-cluster': ClustersComponentsGeneralClockCluster;
      'clusters-components.message-banner-clusters': ClustersComponentsMessageBannerClusters;
      'clusters-components.message-clusters': ClustersComponentsMessageClusters;
      'clusters-components.product-clock-cluster': ClustersComponentsProductClockCluster;
      'combo-promotion.carousel-combo-promotion': ComboPromotionCarouselComboPromotion;
      'combo-promotion.combo-steps': ComboPromotionComboSteps;
      'combo-promotion.combo': ComboPromotionCombo;
      'combo-promotion.repetable-combo': ComboPromotionRepetableCombo;
      'components.components': ComponentsComponents;
      'content-area.content-area-banner-item': ContentAreaContentAreaBannerItem;
      'content-area.content-area-with-sign': ContentAreaContentAreaWithSign;
      'content-area.content-area': ContentAreaContentArea;
      'coupon-banner.coupon-banner': CouponBannerCouponBanner;
      'created-suitcase-empty.created-suitcase-empty': CreatedSuitcaseEmptyCreatedSuitcaseEmpty;
      'cupons.cupom': CuponsCupom;
      'deeplink.deeplinks': DeeplinkDeeplinks;
      'deeplink.invalid-deeplink': DeeplinkInvalidDeeplink;
      'etc.badge-discount': EtcBadgeDiscount;
      'etc.header-etc': EtcHeaderEtc;
      'etc.home-component': EtcHomeComponent;
      'etc.home-composition': EtcHomeComposition;
      'etc.image-content': EtcImageContent;
      'etc.products-grid-filter': EtcProductsGridFilter;
      'fidelity.benefits-item': FidelityBenefitsItem;
      'fidelity.benefits': FidelityBenefits;
      'fidelity.club-example': FidelityClubExample;
      'fidelity.faq-item': FidelityFaqItem;
      'fidelity.faq': FidelityFaq;
      'fidelity.how-work-item': FidelityHowWorkItem;
      'fidelity.how-work': FidelityHowWork;
      'fidelity.invite': FidelityInvite;
      'fidelity.join-club': FidelityJoinClub;
      'fidelity.level-description-item': FidelityLevelDescriptionItem;
      'fidelity.level-description': FidelityLevelDescription;
      'fidelity.points-for-register': FidelityPointsForRegister;
      'fidelity.rate-club': FidelityRateClub;
      'fidelity.redirect-button': FidelityRedirectButton;
      'fidelity.user-info': FidelityUserInfo;
      'full-look-carousel.full-look-carousel-item': FullLookCarouselFullLookCarouselItem;
      'full-look-carousel.full-look-carousel': FullLookCarouselFullLookCarousel;
      'grid-category-listing.grid-category-listing': GridCategoryListingGridCategoryListing;
      'grid-category-listing.grind-item': GridCategoryListingGrindItem;
      'highlight.highlight-list': HighlightHighlightList;
      'highlight.highlight-optional-item': HighlightHighlightOptionalItem;
      'highlight.highlight-optional-media': HighlightHighlightOptionalMedia;
      'highlight.highlight': HighlightHighlight;
      'hrg-components.hrg-banner-item': HrgComponentsHrgBannerItem;
      'hrg-components.hrg-carousel-banner': HrgComponentsHrgCarouselBanner;
      'hrg-components.hrg-categories-banners': HrgComponentsHrgCategoriesBanners;
      'hrg-components.hrg-category-banner': HrgComponentsHrgCategoryBanner;
      'hrg-components.hrg-dynamic-navigation-banner-item': HrgComponentsHrgDynamicNavigationBannerItem;
      'hrg-components.hrg-dynamic-navigation-banners': HrgComponentsHrgDynamicNavigationBanners;
      'hrg-components.hrg-menu-collections-group': HrgComponentsHrgMenuCollectionsGroup;
      'hrg-components.hrg-menu-content-header-banners': HrgComponentsHrgMenuContentHeaderBanners;
      'hrg-components.hrg-menu-floating-button': HrgComponentsHrgMenuFloatingButton;
      'hrg-components.hrg-menu-list-item': HrgComponentsHrgMenuListItem;
      'hrg-components.hrg-menu-segment-item': HrgComponentsHrgMenuSegmentItem;
      'hrg-components.hrg-menu-segment-section': HrgComponentsHrgMenuSegmentSection;
      'hrg-components.hrg-menu-tab': HrgComponentsHrgMenuTab;
      'hrg-components.hrg-new-arrivals-section': HrgComponentsHrgNewArrivalsSection;
      'hrg-components.hrg-product-characteristic': HrgComponentsHrgProductCharacteristic;
      'hrg-components.hrg-product-color': HrgComponentsHrgProductColor;
      'hrg-components.hrg-product-menu-carousel': HrgComponentsHrgProductMenuCarousel;
      'hrg-components.hrg-product-menu': HrgComponentsHrgProductMenu;
      'hrg-components.hrg-spot-product': HrgComponentsHrgSpotProduct;
      'hrg-components.hrg-store-window-item': HrgComponentsHrgStoreWindowItem;
      'list-links.list-links-icons-item': ListLinksListLinksIconsItem;
      'list-links.list-links-icons': ListLinksListLinksIcons;
      'list-links.list-links-item': ListLinksListLinksItem;
      'list-links.list-links': ListLinksListLinks;
      'media-kit-banner-list.media-kit-banner-list-item-nv': MediaKitBannerListMediaKitBannerListItemNv;
      'media-kit-banner-list.media-kit-banner-list-nv': MediaKitBannerListMediaKitBannerListNv;
      'media-kit-banner.media-kit-banner-nv': MediaKitBannerMediaKitBannerNv;
      'media-kit-banner.media-kit-banner': MediaKitBannerMediaKitBanner;
      'media-kit-categories-mosaic.media-kit-categories-mosaic-item-nv': MediaKitCategoriesMosaicMediaKitCategoriesMosaicItemNv;
      'media-kit-categories-mosaic.media-kit-categories-mosaic-nv': MediaKitCategoriesMosaicMediaKitCategoriesMosaicNv;
      'media-kit-full-look.media-kit-full-look-banner': MediaKitFullLookMediaKitFullLookBanner;
      'media-kit-product-card.media-kit-product-card': MediaKitProductCardMediaKitProductCard;
      'media-list.media-list-item': MediaListMediaListItem;
      'media-list.media-list': MediaListMediaList;
      'menu-collections.menu-collections': MenuCollectionsMenuCollections;
      'menu-tab.menu-tab-animale': MenuTabMenuTabAnimale;
      'menu-tab.menu-tab-crisbarros': MenuTabMenuTabCrisbarros;
      'menu-tab.menu-tab-farm-etc': MenuTabMenuTabFarmEtc;
      'menu-tab.menu-tab-farm-new': MenuTabMenuTabFarmNew;
      'menu-tab.menu-tab-farm': MenuTabMenuTabFarm;
      'menu-tab.menu-tab-hering': MenuTabMenuTabHering;
      'menu-tab.menu-tab-mariafilo': MenuTabMenuTabMariafilo;
      'menu-tab.menu-tab-nv': MenuTabMenuTabNv;
      'menu-tab.menu-tab-offpremium': MenuTabMenuTabOffpremium;
      'mosaic.categories-mosaic': MosaicCategoriesMosaic;
      'mosaic.category-mosaic-item': MosaicCategoryMosaicItem;
      'mosaic.products-mosaic': MosaicProductsMosaic;
      'my-interests-banner.my-interesses-with-header': MyInterestsBannerMyInteressesWithHeader;
      'my-interests-banner.my-interests-banner': MyInterestsBannerMyInterestsBanner;
      'my-interests-card.my-interests-card': MyInterestsCardMyInterestsCard;
      'notification-center.expanded-notification-message': NotificationCenterExpandedNotificationMessage;
      'notification-center.message-banner': NotificationCenterMessageBanner;
      'notification-center.message': NotificationCenterMessage;
      'notification-center.page-data': NotificationCenterPageData;
      'notification-center.redirect-section': NotificationCenterRedirectSection;
      'notification-step.notification-step': NotificationStepNotificationStep;
      'order-details.order-details-warning-text': OrderDetailsOrderDetailsWarningText;
      'packaging.packaging': PackagingPackaging;
      'payment-warning.payment-warning': PaymentWarningPaymentWarning;
      'pdc-header-etc.pdc-header-etc': PdcHeaderEtcPdcHeaderEtc;
      'personal-showcase.personal-showcase': PersonalShowcasePersonalShowcase;
      'product-carousel-image.product-carousel-image': ProductCarouselImageProductCarouselImage;
      'product-stamp.sport-stamp-collection': ProductStampSportStampCollection;
      'product-stamp.sport-technology-stamp': ProductStampSportTechnologyStamp;
      'product-stamp.stamp': ProductStampStamp;
      'promotion.promotion-card': PromotionPromotionCard;
      'quick-filter.quick-filter-component': QuickFilterQuickFilterComponent;
      'search-step.search-step': SearchStepSearchStep;
      'search-trend.search-trend': SearchTrendSearchTrend;
      'showcase-banner-list.show-case-banner-carrousel': ShowcaseBannerListShowCaseBannerCarrousel;
      'showcase-banner-list.showcase-banner-item': ShowcaseBannerListShowcaseBannerItem;
      'showcase-banner.showcase-banner': ShowcaseBannerShowcaseBanner;
      'showcase-list.show-case-list-item': ShowcaseListShowCaseListItem;
      'showcase-list.show-case-list': ShowcaseListShowCaseList;
      'sign-up-login.sign-up-login': SignUpLoginSignUpLogin;
      'smart-recommendation.insider-recommended-showcase': SmartRecommendationInsiderRecommendedShowcase;
      'spot-product-clock.spot-product-clock': SpotProductClockSpotProductClock;
      'spot-product.spot-product': SpotProductSpotProduct;
      'step.step': StepStep;
      'stories.items': StoriesItems;
      'stories.stories': StoriesStories;
      'suggestions.cards': SuggestionsCards;
      'suggestions.suggestions-card': SuggestionsSuggestionsCard;
      'suggestions.suggestions-title': SuggestionsSuggestionsTitle;
      'suitcase-suggestion.suitcase-suggestion': SuitcaseSuggestionSuitcaseSuggestion;
      'tags.colors': TagsColors;
      'tags.fidelity-tags': TagsFidelityTags;
      'tags.pickup-tag': TagsPickupTag;
      'tags.pix-immediate-tag': TagsPixImmediateTag;
      'tags.pix-tag': TagsPixTag;
      'tags.product-color-tag': TagsProductColorTag;
      'tags.product-tag': TagsProductTag;
      'tags.stamp-tag-product': TagsStampTagProduct;
      'tags.stamp-tag': TagsStampTag;
      'text-content.call-to-action': TextContentCallToAction;
      'text-content.list-text': TextContentListText;
      'text-content.text-content': TextContentTextContent;
      'text-content.title-and-description': TextContentTitleAndDescription;
      'text-field.text-field': TextFieldTextField;
      'tip-bar.animated-tip-bar': TipBarAnimatedTipBar;
      'tip-bar.static-tip-bar': TipBarStaticTipBar;
      'tracking-step.tracking-step-default': TrackingStepTrackingStepDefault;
      'tracking-step.tracking-step': TrackingStepTrackingStep;
      'trend.menu-collections': TrendMenuCollections;
      'trend.search-trend': TrendSearchTrend;
      'video-banner.video-banner': VideoBannerVideoBanner;
      'video-gallery.video-gallery-item': VideoGalleryVideoGalleryItem;
      'video-gallery.video-gallery': VideoGalleryVideoGallery;
    }
  }
}
