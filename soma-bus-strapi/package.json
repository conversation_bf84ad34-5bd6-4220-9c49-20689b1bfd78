{"name": "soma-bus-strapi", "version": "0.1.0", "private": true, "description": "A Strapi application", "license": "MIT", "author": {"name": "A Strapi developer"}, "scripts": {"build": "strapi build", "develop": "strapi develop", "debug": "NODE_OPTIONS='--inspect' yarn strapi develop", "sql-proxy-macos": "./cloud_sql_proxy -instances=soma-checkout:us-central1:soma-bus-cms=tcp:3306 -credential_file=service-account.json", "sql-proxy-windows": "./cloud_sql_proxy -instances=soma-checkout:us-central1:soma-bus-cms=tcp:3306 -credential_file=service-account.json", "start": "node apm-wrapper.js", "strapi": "strapi", "cs": "config-sync"}, "dependencies": {"@elastic/elasticsearch": "^8.15.2", "@strapi-community/strapi-provider-upload-google-cloud-storage": "^4.10.5", "@strapi/plugin-cloud": "4.20.5", "@strapi/plugin-color-picker": "^4.21.0", "@strapi/plugin-graphql": "^4.0.0", "@strapi/plugin-i18n": "4.20.5", "@strapi/plugin-users-permissions": "4.20.5", "@strapi/strapi": "4.20.5", "@strapi/typescript-utils": "^4.21.0", "better-sqlite3": "^9.4.3", "elastic-apm-node": "^4.8.1", "lodash.set": "^4.3.2", "mime-types": "^2.1.27", "mysql": "2.18.1", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "5.3.4", "strapi-plugin-config-sync": "^1.2.3", "strapi-plugin-duplicate-button": "^1.1.14", "strapi-plugin-import-export-entries": "^1.23.1", "strapi-plugin-populate-deep": "^3.0.1", "strapi-plugin-publisher": "^1.5.7", "strapi-plugin-redis": "^1.1.0", "strapi-plugin-rest-cache": "^4.2.9", "strapi-provider-rest-cache-redis": "^4.2.9", "styled-components": "5.3.3"}, "resolutions": {"@types/mime": "3.0.4"}, "overrides": {"@types/mime": "3.0.4"}, "engines": {"node": ">=18.0.0 <=20.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "template": "ecommerce"}}