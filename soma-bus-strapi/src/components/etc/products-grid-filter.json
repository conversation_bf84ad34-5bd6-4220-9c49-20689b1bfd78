{"collectionName": "c_products_grid_filters", "info": {"displayName": "Products Grid Filter", "icon": "apps", "description": ""}, "options": {}, "attributes": {"items": {"type": "component", "repeatable": true, "component": "showcase-list.show-case-list-item"}, "title": {"type": "string"}, "subtitle": {"type": "string"}, "call_to_action": {"type": "string"}, "filter_category_or_cluster": {"type": "string"}, "order_by_intelligent_search": {"type": "enumeration", "enum": ["OrderByBestDiscountDESC", "OrderByPriceASC", "OrderByPriceDESC", "OrderByReleaseDateDESC", "OrderByTopSaleDESC"]}, "background_color": {"type": "customField", "regex": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", "customField": "plugin::color-picker.color"}, "foreground_color": {"type": "customField", "regex": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", "customField": "plugin::color-picker.color"}, "banner_name_ga4": {"type": "string", "regex": ".+\\/\\/\\/.+", "required": true}}}