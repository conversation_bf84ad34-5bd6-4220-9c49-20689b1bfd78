{"collectionName": "c_video_gallery_items", "info": {"displayName": "Video Gallery Item", "description": ""}, "options": {}, "attributes": {"image": {"type": "media", "multiple": false, "required": true, "allowedTypes": ["images"]}, "video_url": {"type": "string", "required": true}, "title": {"type": "string"}, "description": {"type": "string"}, "navigate_to": {"type": "text"}, "banner_name_ga4": {"type": "string", "regex": ".+\\/\\/\\/.+", "required": false}}}