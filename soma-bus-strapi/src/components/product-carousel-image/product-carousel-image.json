{"collectionName": "c_product_carousel_images", "info": {"displayName": "Product Carousel Image", "icon": "landscape", "description": ""}, "options": {}, "attributes": {"title": {"type": "string", "required": true}, "subtitle": {"type": "text", "required": true}, "filter_category_or_cluster": {"type": "string", "required": true}, "call_to_action": {"type": "string"}, "orderby": {"type": "enumeration", "enum": ["OrderByBestDiscountDESC", "OrderByPriceASC", "OrderByPriceDESC", "OrderByReleaseDateDESC", "OrderByTopSaleDESC"]}, "text_color": {"type": "customField", "regex": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", "customField": "plugin::color-picker.color"}, "background_color": {"type": "customField", "regex": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", "customField": "plugin::color-picker.color"}, "image": {"allowedTypes": ["images"], "type": "media", "multiple": false, "required": true}, "banner_name_ga4": {"type": "string", "regex": ".+\\/\\/\\/.+", "required": true}}}