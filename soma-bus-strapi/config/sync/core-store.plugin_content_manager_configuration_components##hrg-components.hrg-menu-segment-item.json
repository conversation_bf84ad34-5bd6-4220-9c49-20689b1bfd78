{"key": "plugin_content_manager_configuration_components::hrg-components.hrg-menu-segment-item", "value": {"settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "title", "defaultSortBy": "id", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": false, "sortable": false}}, "title": {"edit": {"label": "Título do <PERSON>", "description": "O título que o item de lista irá ter", "placeholder": "Ex: <PERSON><PERSON> + <PERSON><PERSON>", "visible": true, "editable": true}, "list": {"label": "title", "searchable": true, "sortable": true}}, "navigate_to": {"edit": {"label": "Navegação dinâmica", "description": "A navegação dinâmica que será realizada ao clicar no item", "placeholder": "Ex: plp/productclusterids/862", "visible": true, "editable": true}, "list": {"label": "navigate_to", "searchable": true, "sortable": true}}, "collections_group_page_title": {"edit": {"label": "Titulo da página de grupos de categorias/coleções", "description": "Esse é o título da página de grupos de categorias/coleções, será exibido logo acima, como um header", "placeholder": "Ex: <PERSON><PERSON><PERSON>", "visible": true, "editable": true}, "list": {"label": "collections_group_page_title", "searchable": true, "sortable": true}}, "collections_groups": {"edit": {"label": "Grupos de coleções/categorias", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "collections_groups", "searchable": false, "sortable": false}}, "tag_text": {"edit": {"label": "<PERSON><PERSON> da Tag", "description": "Insira aqui se esse item deve ter uma Tag", "placeholder": "Ex: Até R$199,99", "visible": true, "editable": true}, "list": {"label": "tag_text", "searchable": true, "sortable": true}}, "title_color": {"edit": {"label": "Cor do título do item", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "title_color", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "title", "navigate_to", "collections_groups"], "edit": [[{"name": "title", "size": 6}, {"name": "title_color", "size": 6}], [{"name": "navigate_to", "size": 12}], [{"name": "collections_groups", "size": 12}], [{"name": "collections_group_page_title", "size": 6}, {"name": "tag_text", "size": 6}]]}, "uid": "hrg-components.hrg-menu-segment-item", "isComponent": true}, "type": "object", "environment": null, "tag": null}