{"key": "plugin_content_manager_configuration_components::hrg-components.hrg-menu-list-item", "value": {"settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "title", "defaultSortBy": "title", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": false, "sortable": false}}, "title": {"edit": {"label": "Título do <PERSON>", "description": "O título que o item de lista irá ter", "placeholder": "Ex: <PERSON><PERSON> + <PERSON><PERSON>", "visible": true, "editable": true}, "list": {"label": "title", "searchable": true, "sortable": true}}, "title_color": {"edit": {"label": "Cor do título", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "title_color", "searchable": true, "sortable": true}}, "navigate_to": {"edit": {"label": "Navegação dinâmica", "description": "A navegação dinâmica que será realizada ao clicar no item", "placeholder": "Ex: plp/productclusterids/862", "visible": true, "editable": true}, "list": {"label": "navigate_to", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "title", "title_color", "navigate_to"], "edit": [[{"name": "title", "size": 6}, {"name": "title_color", "size": 6}], [{"name": "navigate_to", "size": 6}]]}, "uid": "hrg-components.hrg-menu-list-item", "isComponent": true}, "type": "object", "environment": null, "tag": null}