{"key": "plugin_content_manager_configuration_components::hrg-components.hrg-banner-item", "value": {"settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "navigate_to", "defaultSortBy": "navigate_to", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": false, "sortable": false}}, "navigate_to": {"edit": {"label": "Navegação Dinâmica", "description": "Link para a coleção", "placeholder": "plp/productclusterids/2693", "visible": true, "editable": true}, "list": {"label": "navigate_to", "searchable": true, "sortable": true}}, "banner_name_ga4": {"edit": {"label": "Banner Name GA4", "description": "PromotionName e CreativeName para eventos de analytics, separados por ///", "placeholder": "PromotionName///CreativeName", "visible": true, "editable": true}, "list": {"label": "banner_name_ga4", "searchable": true, "sortable": true}}, "video_url": {"edit": {"label": "Video URL", "description": "Link do vídeo usado no Banner", "placeholder": "https://player.vimeo.com...", "visible": true, "editable": true}, "list": {"label": "video_url", "searchable": true, "sortable": true}}, "media": {"edit": {"label": "media", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "media", "searchable": false, "sortable": false}}, "title": {"edit": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Título <PERSON>", "placeholder": "Movimento", "visible": true, "editable": true}, "list": {"label": "title", "searchable": true, "sortable": true}}, "subtitle": {"edit": {"label": "Subtítulo", "description": "Subtítulo do banner", "placeholder": "INV 2025", "visible": true, "editable": true}, "list": {"label": "subtitle", "searchable": true, "sortable": true}}, "call_to_action": {"edit": {"label": "CAT Texto", "description": "Descrição do CAT da coleção", "placeholder": "Confira a coleção", "visible": true, "editable": true}, "list": {"label": "call_to_action", "searchable": true, "sortable": true}}, "store_window": {"edit": {"label": "<PERSON><PERSON><PERSON> da <PERSON>", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "store_window", "searchable": false, "sortable": false}}}, "layouts": {"list": ["id", "navigate_to", "banner_name_ga4", "video_url"], "edit": [[{"name": "navigate_to", "size": 6}, {"name": "banner_name_ga4", "size": 6}], [{"name": "video_url", "size": 6}, {"name": "media", "size": 6}], [{"name": "title", "size": 6}, {"name": "subtitle", "size": 6}], [{"name": "call_to_action", "size": 6}], [{"name": "store_window", "size": 12}]]}, "uid": "hrg-components.hrg-banner-item", "isComponent": true}, "type": "object", "environment": null, "tag": null}