{"key": "plugin_content_manager_configuration_components::hrg-components.hrg-store-window-item", "value": {"settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "filter_category_or_cluster", "defaultSortBy": "filter_category_or_cluster", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": false, "sortable": false}}, "filter_category_or_cluster": {"edit": {"label": "Filtro de Categoria ou Coleção", "description": "Categoria ou coleção que os produtos serão filtrados", "placeholder": "Ex: /productclusterids/862", "visible": true, "editable": true}, "list": {"label": "filter_category_or_cluster", "searchable": true, "sortable": true}}, "media": {"edit": {"label": "media", "description": "Imagem do produto apresentado no Banner", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "media", "searchable": false, "sortable": false}}, "navigate_to": {"edit": {"label": "Navegação dinâmica", "description": "Código do Item que está sendo apresentado", "placeholder": "2693", "visible": true, "editable": true}, "list": {"label": "navigate_to", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "filter_category_or_cluster", "media", "navigate_to"], "edit": [[{"name": "filter_category_or_cluster", "size": 6}, {"name": "media", "size": 6}], [{"name": "navigate_to", "size": 6}]]}, "uid": "hrg-components.hrg-store-window-item", "isComponent": true}, "type": "object", "environment": null, "tag": null}