{"key": "plugin_content_manager_configuration_components::full-look-carousel.full-look-carousel-item", "value": {"settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "banner_name_ga4", "defaultSortBy": "banner_name_ga4", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": false, "sortable": false}}, "image": {"edit": {"label": "Imagem", "description": "Imagem do look completo que vai aparecer no carrossel", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "image", "searchable": false, "sortable": false}}, "product_ids": {"edit": {"label": "Ids dos Produtos", "description": "Lista de IDs dos produtos do look completo. Obs: Usar ; (ponto e vírgula) para separar os IDs", "placeholder": "Ex: 1234;5678;9012;0987", "visible": true, "editable": true}, "list": {"label": "product_ids", "searchable": true, "sortable": true}}, "banner_name_ga4": {"edit": {"label": "Nome do banner no GA4", "description": "PromotionName e CreativeName para eventos de Analytics separados por ///", "placeholder": "PROMOTION NAME///CREATIVE NAME", "visible": true, "editable": true}, "list": {"label": "banner_name_ga4", "searchable": true, "sortable": true}}, "button_text": {"edit": {"label": "button_text", "description": "Texto do botão, caso não preenchido exibe um botão com o ícone de +", "placeholder": "<PERSON><PERSON> esse look", "visible": true, "editable": true}, "list": {"label": "button_text", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "image", "product_ids", "banner_name_ga4"], "edit": [[{"name": "image", "size": 6}, {"name": "product_ids", "size": 6}], [{"name": "banner_name_ga4", "size": 6}, {"name": "button_text", "size": 6}]]}, "uid": "full-look-carousel.full-look-carousel-item", "isComponent": true}, "type": "object", "environment": null, "tag": null}