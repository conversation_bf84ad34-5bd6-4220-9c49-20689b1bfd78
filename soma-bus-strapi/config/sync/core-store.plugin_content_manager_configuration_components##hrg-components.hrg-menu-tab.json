{"key": "plugin_content_manager_configuration_components::hrg-components.hrg-menu-tab", "value": {"settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "title", "defaultSortBy": "title", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": false, "sortable": false}}, "title": {"edit": {"label": "<PERSON><PERSON><PERSON><PERSON> da tab", "description": "O título que essa tab irá ter", "placeholder": "Ex: <PERSON><PERSON><PERSON>", "visible": true, "editable": true}, "list": {"label": "title", "searchable": true, "sortable": true}}, "content": {"edit": {"label": "<PERSON><PERSON><PERSON><PERSON> da tab", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "name"}, "list": {"label": "content", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "title", "content"], "edit": [[{"name": "title", "size": 6}, {"name": "content", "size": 6}]]}, "uid": "hrg-components.hrg-menu-tab", "isComponent": true}, "type": "object", "environment": null, "tag": null}