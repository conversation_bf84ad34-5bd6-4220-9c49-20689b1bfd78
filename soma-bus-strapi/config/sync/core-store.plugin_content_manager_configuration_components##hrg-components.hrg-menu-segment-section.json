{"key": "plugin_content_manager_configuration_components::hrg-components.hrg-menu-segment-section", "value": {"settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "section_title", "defaultSortBy": "section_title", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": false, "sortable": false}}, "section_title": {"edit": {"label": "T<PERSON><PERSON><PERSON> da <PERSON>ção", "description": "O título que o grupo de itens irá ter", "placeholder": "Ex: <PERSON><PERSON>", "visible": true, "editable": true}, "list": {"label": "section_title", "searchable": true, "sortable": true}}, "items": {"edit": {"label": "<PERSON><PERSON>", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "items", "searchable": false, "sortable": false}}, "section_title_color": {"edit": {"label": "Cor do título da seção", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "section_title_color", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "section_title", "items", "section_title_color"], "edit": [[{"name": "section_title", "size": 6}], [{"name": "items", "size": 12}], [{"name": "section_title_color", "size": 6}]]}, "uid": "hrg-components.hrg-menu-segment-section", "isComponent": true}, "type": "object", "environment": null, "tag": null}