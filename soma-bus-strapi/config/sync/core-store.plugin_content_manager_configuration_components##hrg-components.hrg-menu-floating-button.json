{"key": "plugin_content_manager_configuration_components::hrg-components.hrg-menu-floating-button", "value": {"settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "text", "defaultSortBy": "text", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": false, "sortable": false}}, "text": {"edit": {"label": "Texto do botão", "description": "Ex: O texto que será exibido dentro do botão flutuante", "placeholder": "Ex: Passear pela loja", "visible": true, "editable": true}, "list": {"label": "text", "searchable": true, "sortable": true}}, "navigate_to": {"edit": {"label": "Navegação dinâmica", "description": "A navegação dinâmica que será realizada ao clicar no botão", "placeholder": "Ex: plp/productclusterids/862", "visible": true, "editable": true}, "list": {"label": "navigate_to", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "text", "navigate_to"], "edit": [[{"name": "text", "size": 6}, {"name": "navigate_to", "size": 6}]]}, "uid": "hrg-components.hrg-menu-floating-button", "isComponent": true}, "type": "object", "environment": null, "tag": null}