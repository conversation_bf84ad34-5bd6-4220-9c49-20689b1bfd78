{"key": "plugin_content_manager_configuration_components::hrg-components.hrg-product-menu-carousel", "value": {"settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "id", "defaultSortBy": "id", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": false, "sortable": false}}, "products": {"edit": {"label": "products", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "products", "searchable": false, "sortable": false}}}, "layouts": {"list": ["id", "products"], "edit": [[{"name": "products", "size": 12}]]}, "uid": "hrg-components.hrg-product-menu-carousel", "isComponent": true}, "type": "object", "environment": null, "tag": null}