{"key": "plugin_content_manager_configuration_components::clock.clock-maria-filo", "value": {"settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "title", "defaultSortBy": "title", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": false, "sortable": false}}, "is_active": {"edit": {"label": "is_active", "description": "Indica se o reloginho está ativo ou não.", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "is_active", "searchable": true, "sortable": true}}, "title": {"edit": {"label": "title", "description": "Texto para botão do reloginho.", "placeholder": "Ex: Promoção de outono", "visible": true, "editable": true}, "list": {"label": "title", "searchable": true, "sortable": true}}, "navigate_to": {"edit": {"label": "navigate_to", "description": "Navegação dinâmica realizada ao clicar no botão do reloginho.", "placeholder": "Ex: plp/productclusternames/altoinverno24", "visible": true, "editable": true}, "list": {"label": "navigate_to", "searchable": true, "sortable": true}}, "coupon": {"edit": {"label": "coupon", "description": "Cupom referente ao reloginho.", "placeholder": "Ex: 20NOAPP", "visible": true, "editable": true}, "list": {"label": "coupon", "searchable": true, "sortable": true}}, "button_text": {"edit": {"label": "button_text", "description": "Texto para botão do reloginho.", "placeholder": "Ex: Aplicar cupom", "visible": true, "editable": true}, "list": {"label": "button_text", "searchable": true, "sortable": true}}, "start_time": {"edit": {"label": "start_time", "description": "<PERSON><PERSON><PERSON><PERSON> inicial para o reloginho.", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "start_time", "searchable": true, "sortable": true}}, "end_time": {"edit": {"label": "end_time", "description": "Horário final para o reloginho.", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "end_time", "searchable": true, "sortable": true}}, "text_color": {"edit": {"label": "text_color", "description": "Cor para o texto do reloginho.", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "text_color", "searchable": true, "sortable": true}}, "background_color": {"edit": {"label": "background_color", "description": "Cor de fundo para o reloginho.", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "background_color", "searchable": true, "sortable": true}}, "banner_name_ga4": {"edit": {"label": "Nome do banner no GA4", "description": "PromotionName e CreativeName para eventos de analytics separados por ///", "placeholder": "PROMOTION NAME///CREATIVE NAME", "visible": true, "editable": true}, "list": {"label": "banner_name_ga4", "searchable": true, "sortable": true}}, "subtitle": {"edit": {"label": "subtitle", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "subtitle", "searchable": true, "sortable": true}}, "rules_button_text": {"edit": {"label": "rules_button_text", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "rules_button_text", "searchable": true, "sortable": true}}, "rules_description": {"edit": {"label": "rules_description", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "rules_description", "searchable": true, "sortable": true}}, "show_coupon": {"edit": {"label": "show_coupon", "description": "Mostrar cupom apenas se estiver ativo", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "show_coupon", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "is_active", "title", "navigate_to"], "edit": [[{"name": "is_active", "size": 4}], [{"name": "title", "size": 6}, {"name": "button_text", "size": 6}], [{"name": "coupon", "size": 6}, {"name": "navigate_to", "size": 6}], [{"name": "text_color", "size": 6}, {"name": "background_color", "size": 6}], [{"name": "start_time", "size": 6}, {"name": "end_time", "size": 6}], [{"name": "banner_name_ga4", "size": 6}, {"name": "subtitle", "size": 6}], [{"name": "rules_button_text", "size": 6}, {"name": "rules_description", "size": 6}], [{"name": "show_coupon", "size": 4}]]}, "uid": "clock.clock-maria-filo", "isComponent": true}, "type": "object", "environment": null, "tag": null}