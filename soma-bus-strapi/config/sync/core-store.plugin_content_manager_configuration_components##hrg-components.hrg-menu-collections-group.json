{"key": "plugin_content_manager_configuration_components::hrg-components.hrg-menu-collections-group", "value": {"settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "collection_title", "defaultSortBy": "id", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": false, "sortable": false}}, "collection_title": {"edit": {"label": "Título do grupo", "description": "O título que o grupo de categorias/coleções irá ter", "placeholder": "Ex: Collab", "visible": true, "editable": true}, "list": {"label": "collection_title", "searchable": true, "sortable": true}}, "collection_title_color": {"edit": {"label": "Cor do título do grupo", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "collection_title_color", "searchable": true, "sortable": true}}, "items": {"edit": {"label": "<PERSON><PERSON>", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "items", "searchable": false, "sortable": false}}}, "layouts": {"list": ["id", "items", "collection_title", "collection_title_color"], "edit": [[{"name": "collection_title", "size": 6}, {"name": "collection_title_color", "size": 6}], [{"name": "items", "size": 12}]]}, "uid": "hrg-components.hrg-menu-collections-group", "isComponent": true}, "type": "object", "environment": null, "tag": null}