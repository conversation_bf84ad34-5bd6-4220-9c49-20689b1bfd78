{"key": "plugin_content_manager_configuration_content_types::api::hrg-menu-contents-hering.hrg-menu-contents-hering", "value": {"settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "name", "defaultSortBy": "name", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "name": {"edit": {"label": "Nome do documento", "description": "", "placeholder": "Ex: <PERSON><PERSON><PERSON><PERSON> 24/09", "visible": true, "editable": true}, "list": {"label": "name", "searchable": true, "sortable": true}}, "header_banners": {"edit": {"label": "Banners do Header", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "header_banners", "searchable": false, "sortable": false}}, "components": {"edit": {"label": "components", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "components", "searchable": false, "sortable": false}}, "floating_button": {"edit": {"label": "Botão flutuante", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "floating_button", "searchable": false, "sortable": false}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}, "createdBy": {"edit": {"label": "created<PERSON>y", "description": "", "placeholder": "", "visible": false, "editable": true, "mainField": "firstname"}, "list": {"label": "created<PERSON>y", "searchable": true, "sortable": true}}, "updatedBy": {"edit": {"label": "updatedBy", "description": "", "placeholder": "", "visible": false, "editable": true, "mainField": "firstname"}, "list": {"label": "updatedBy", "searchable": true, "sortable": true}}}, "layouts": {"edit": [[{"name": "name", "size": 6}], [{"name": "header_banners", "size": 12}], [{"name": "components", "size": 12}], [{"name": "floating_button", "size": 12}]], "list": ["id", "createdAt", "updatedAt", "name"]}, "uid": "api::hrg-menu-contents-hering.hrg-menu-contents-hering"}, "type": "object", "environment": null, "tag": null}