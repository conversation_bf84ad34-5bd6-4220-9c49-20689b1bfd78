{"key": "plugin_content_manager_configuration_components::hrg-components.hrg-product-menu", "value": {"settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "title", "defaultSortBy": "title", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": false, "sortable": false}}, "media": {"edit": {"label": "media", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "media", "searchable": false, "sortable": false}}, "title": {"edit": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "title", "searchable": true, "sortable": true}}, "call_to_action": {"edit": {"label": "Call to Action", "description": "Texto do botão de ação", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "call_to_action", "searchable": true, "sortable": true}}, "navigate_to": {"edit": {"label": "Navegação dinâmica", "description": "ID do Produto pra onde vai navegar", "placeholder": "2938", "visible": true, "editable": true}, "list": {"label": "call_to_action", "searchable": true, "sortable": true}}, "product_characteristics": {"edit": {"label": "Características", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "product_characteristics", "searchable": false, "sortable": false}}, "product_colors": {"edit": {"label": "product_colors", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "product_colors", "searchable": false, "sortable": false}}, "banner_name_ga4": {"edit": {"label": "banner_name_ga4", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "banner_name_ga4", "searchable": true, "sortable": true}}, "combo_description": {"edit": {"label": "combo_description", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "combo_description", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "media", "title", "product_characteristics"], "edit": [[{"name": "media", "size": 6}, {"name": "title", "size": 6}], [{"name": "call_to_action", "size": 6}, {"name": "navigate_to", "size": 6}], [{"name": "banner_name_ga4", "size": 6}], [{"name": "product_characteristics", "size": 12}], [{"name": "product_colors", "size": 12}], [{"name": "combo_description", "size": 6}]]}, "uid": "hrg-components.hrg-product-menu", "isComponent": true}, "type": "object", "environment": null, "tag": null}