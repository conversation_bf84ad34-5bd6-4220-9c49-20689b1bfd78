import 'dart:io';

import 'package:appsflyer_sdk/appsflyer_sdk.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';

import '../utils/generate_sha256.dart';

class AzzasAppsflyer {
  static late final AppsflyerSdk appsflyerSdk;

  AzzasAppsflyer();

  static Future<void> initializeAppsFlyer(
      {required String afDevKey, required String appId}) async {
    if (kIsWeb) return;

    final AppsFlyerOptions options = AppsFlyerOptions(
        afDevKey: afDevKey,
        appId: appId,
        showDebug: true,
        timeToWaitForATTUserAuthorization: 15,
        manualStart: true);

    appsflyerSdk = AppsflyerSdk(options);

    await appsflyerSdk.initSdk(
        registerConversionDataCallback: true,
        registerOnAppOpenAttributionCallback: true,
        registerOnDeepLinkingCallback: true);

    var ttt;

    appsflyerSdk.startSDK(
      onSuccess: () {
        debugPrint("AppsFlyer SDK initialized successfully.");
      },
      onError: (int errorCode, String errorMessage) {
        debugPrint(
            "Error initializing AppsFlyer SDK: Code $errorCode - $errorMessage");
      },
    );

    final uninstallToken = await _getUninstallToken();
    if (uninstallToken != null) {
      appsflyerSdk.updateServerUninstallToken(uninstallToken);
    }
  }

  void setCustomerUserId({required String email}) {
    try {
      if (kIsWeb) return;
      final emailSha256 = GenerateSha256.generateSha256(email);
      appsflyerSdk.setCustomerUserId(emailSha256);
    } catch (e) {
      debugPrint("Error setting customer user id: $e");
    }
  }

  Future<String?> getAppsFlyerUID() async {
    final AppsFlyerId = await appsflyerSdk.getAppsFlyerUID();
    return AppsFlyerId;
  }

  static Future<String?> _getUninstallToken() async {
    if (Platform.isAndroid) {
      return FirebaseMessaging.instance.getToken();
    } else if (Platform.isIOS) {
      return FirebaseMessaging.instance.getAPNSToken();
    } else {
      return null;
    }
  }
}
