class AddPaymentInfoParams {
  final bool preFilled;
  final double totalDiscount;
  final double subtotal;
  final String paymentType;
  final String itemStrLine;
  final String itemLine;

  AddPaymentInfoParams({
    required this.preFilled,
    required this.totalDiscount,
    required this.subtotal,
    required this.paymentType,
    required this.itemStrLine,
    required this.itemLine,
  });

  Map<String, Object> toJson() {
    return {
      'pre_filled': preFilled.toString(),
      'total_discount': totalDiscount,
      'subtotal': subtotal,
      'payment_type': paymentType,
      'item_str_line': itemStrLine.toString(),
      'item_line': itemLine,
    };
  }
}
