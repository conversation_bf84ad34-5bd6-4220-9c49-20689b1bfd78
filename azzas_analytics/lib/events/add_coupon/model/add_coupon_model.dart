class AddCouponModel {
  final String? coupon;
  final String? sellerCodeName;
  final String message;

  AddCouponModel({
    this.coupon,
    this.sellerCodeName,
    required this.message,
  });

  Map<String, Object> toJson() {
    return {
      'seller_cod_name': sellerCodeName.toString(),
      'coupon_name': coupon.toString(),
      'coupon_message': message,
      'region': 'checkout',
    };
  }
}
