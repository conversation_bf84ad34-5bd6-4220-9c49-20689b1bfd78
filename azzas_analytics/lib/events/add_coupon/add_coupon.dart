import 'package:azzas_analytics/events/add_coupon/index.dart';
import 'package:azzas_analytics/services/analytics_logger.dart';
import 'package:firebase_analytics/firebase_analytics.dart';

class AddCoupon {
  static Future<void> send(AddCouponModel model) async {
    await FirebaseAnalytics.instance.logEvent(
      name: 'add_coupon',
      parameters: model.toJson(),
    );

    AnalyticsLogger.log(
      '''
        ====================================================
        Evento enviado: addCoupon
        ====================================================
        sellerCodName: ${model.sellerCodeName}
        ----------------------------------------------------
        couponName: ${model.coupon}
        ----------------------------------------------------
        couponMessage: ${model.message}
        ====================================================
      ''',
    );
  }
}
