import '../../../services/analytics_service.dart';

class AccountEvents {
  static Future<void> logMenuAccount({
    required String opcaoClicada,
    String? local,
  }) async {
    await AnalyticsService.trackEvent('select_content', {
      'local': local ?? '',
      'content_type': 'seu-perfil:$opcaoClicada',
    });
  }

  static Future<void> logEmailVerification({
    required String action,
    String? local,
  }) async {
    await AnalyticsService.trackEvent('select_content', {
      'local': local ?? '',
      'content_type': 'verificacao-email:$action',
    });
  }

  static Future<void> logSignUpStep({
    required String step,
    String? local,
  }) async {
    await AnalyticsService.trackEvent('select_content', {
      'local': local ?? '',
      'content_type': 'cadastro:step:$step',
    });
  }

  static Future<void> logSignUpMethod({
    required String method,
    String? local,
  }) async {
    await AnalyticsService.trackEvent('sign_up', {
      'local': local ?? '',
      'method': method,
    });
  }

  static Future<void> logCreateAccount({
    required String opcaoClicada,
    String? local,
  }) async {
    await AnalyticsService.trackEvent('select_content', {
      'local': local ?? '',
      'content_type': 'crie-sua-conta:$opcaoClicada',
    });
  }

  static Future<void> logLogin({
    required String method,
    String? local,
  }) async {
    await AnalyticsService.trackEvent('login', {
      'local': local ?? '',
      'method': method,
    });
  }
}
