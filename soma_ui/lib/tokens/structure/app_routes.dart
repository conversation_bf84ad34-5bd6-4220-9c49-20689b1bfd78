class AppRoutes {
  final String home;
  final String splash;
  final String productDetails;
  final String webview;
  final String searchResult;
  final String login;
  final String passwordCheck;
  final String emailCheck;
  final String cpfCheck;
  final String nameCheck;
  final String pinCode;
  final String phoneCheck;
  final String orders;
  final String trackOrder;
  final String addCoupons;
  final String paymentsInstallments;
  final String paymentMethod;
  final String paymentNewCreditCard;
  final String shippingRegistration;
  final String addressRegistration;
  final String confirmationOrder;
  final String orderReview;
  final String checkout;
  final String filters;
  final String userData;
  final String wishlist;
  final String explore;
  final String emptySearch;
  final String genericError;
  final String accountDelete;
  final String? deliveryPackagesSelection;
  final String? cashbackWallet;
  final String? cashbackExtract;
  final String? cashbackStatus;
  final String? cashbackDetails;
  final String? cashbackFaq;
  final String? cashbackFaqDetails;
  final String? myInterests;
  final String myInterestsPDC;
  final String? wishlistFolderProducts;
  final String tapume;
  final String? lpCustom;
  final String creditVoucher;
  final String checkingAccount;
  final String videoContent;
  final String pixPayment;
  final String occasion;
  final String? favoriteStores;
  final String onlineOrderTrack;
  final String? checkAvailibilityInStore;
  final String? searchImage;
  final String paymentLink;
  final String paymentLinkOrderConfirmation;
  final String combos;
  final String callCenter;
  final String orderReturns;
  final String turbo;
  final String suggestions;
  final String fullLook;
  final String aiAssistant;
  final String liveTransmission;
  final String transitionEtc;

  const AppRoutes({
    this.home = '/',
    this.splash = '/splash',
    this.productDetails = '/productDetails',
    this.webview = '/webview',
    this.searchResult = '/searchResult',
    this.login = '/login',
    this.passwordCheck = '/passwordCheck',
    this.emailCheck = '/emailCheck',
    this.cpfCheck = '/CPFCheck',
    this.nameCheck = '/nameCheck',
    this.phoneCheck = '/phoneCheck',
    this.pinCode = '/pinCode',
    this.orders = '/orders',
    this.trackOrder = '/trackOrder',
    this.addCoupons = '/addCoupons',
    this.paymentsInstallments = '/paymentsInstallments',
    this.paymentMethod = '/paymentMethod',
    this.paymentNewCreditCard = '/paymentNewCreditCard',
    this.shippingRegistration = '/shippingRegistration',
    this.addressRegistration = '/addressRegistration',
    this.confirmationOrder = '/confirmationOrder',
    this.orderReview = '/orderReview',
    this.checkout = '/checkout',
    this.filters = '/filters',
    this.userData = '/userData',
    this.wishlist = '/wishlist',
    this.explore = '/explore',
    this.emptySearch = '/empty_search',
    this.genericError = '/generic_error',
    this.accountDelete = '/account_delete',
    this.deliveryPackagesSelection,
    this.cashbackWallet,
    this.cashbackExtract,
    this.cashbackStatus,
    this.cashbackDetails,
    this.cashbackFaq,
    this.cashbackFaqDetails,
    this.myInterests = '/myInterests',
    this.myInterestsPDC = '/my_interest_products',
    this.wishlistFolderProducts,
    this.lpCustom = '/lp-custom',
    this.tapume = '/tapume',
    this.creditVoucher = '/creditVoucher',
    this.checkingAccount = '/checkingAccount',
    this.videoContent = '/videoContent',
    this.pixPayment = '/pixPayment',
    this.occasion = '/occasion',
    this.favoriteStores = '/favoriteStores',
    this.onlineOrderTrack = '/onlineOrderTrack',
    this.checkAvailibilityInStore = '/checkAvailabilityInStore',
    this.searchImage = '/searchImage',
    this.paymentLink = '/paymentLink',
    this.paymentLinkOrderConfirmation = '/paymentLinkOrderConfirmation',
    this.combos = '/combos',
    this.callCenter = '/callcenter',
    this.orderReturns = '/order-returns',
    this.turbo = '/select-address',
    this.suggestions = '/suggestions-page',
    this.fullLook = '/full-look-page',
    this.aiAssistant = '/ai-assistant',
    this.liveTransmission = '/live',
    this.transitionEtc = '/transitionEtc'
  });
}
