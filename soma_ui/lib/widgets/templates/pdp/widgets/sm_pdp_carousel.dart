import 'package:flutter/material.dart';

import 'package:soma_core/soma_core.dart';
import 'package:soma_ui/soma_ui.dart';

import 'package:soma_core/modules/catalog/controllers/catalog_controller.dart';
import 'package:soma_core/modules/catalog/controllers/wishlist_controller.dart';
import 'package:soma_ui/widgets/templates/pdp/controller/carousel_other_look_controller.dart';
import 'package:soma_ui/widgets/templates/pdp/full_look/sm_pdp_full_look_controller.dart';

class SMPDPCarousel extends StatefulWidget {
  final PageController imageScrollController;
  final String productHeroID;
  final Product product;

  const SMPDPCarousel({
    super.key,
    required this.productHeroID,
    required this.imageScrollController,
    required this.product,
  });

  @override
  State<SMPDPCarousel> createState() => _SMPDPCarouselState();
}

class _SMPDPCarouselState extends State<SMPDPCarousel>
    with
        TickerProviderStateMixin,
        DesignTokensStateMixin,
        SomaCoreStateMixin,
        AnalyticsEventDispatcherStateMixin,
        AppRoutesStateMixin,
        AppAssetsStateMixin {
  late final AnimationController _controllerReset;
  late final AnimationController _heartAnimationController;
  late WishlistController _wishlist;

  final _transformationController = TransformationController();

  bool showHeart = false;
  Animation<Matrix4>? _animationReset;
  int? position;

  @override
  void initState() {
    super.initState();

    _wishlist = wishlistController;
    widget.imageScrollController.addListener(_onScroll);

    _heartAnimationController = AnimationController(vsync: this);
    _heartAnimationController.addListener(() {
      _heartAnimationController.forward().whenComplete(() {
        if (showHeart && !config.appFeaturesConfig.wishlistV2) {
          showSnackBar(
            SMSnackBarGetX(
              textLabel: termsAndMessages.wishlistSavedSuccessfuly,
              designTokens: tokens,
            ),
          );
        }

        setState(() => showHeart = false);
      });
    });

    _controllerReset = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );
  }

  @override
  void dispose() {
    _transformationController.dispose();
    _controllerReset.dispose();
    _heartAnimationController.dispose();
    widget.imageScrollController.removeListener(_onScroll);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    PDPNotifier? notifier;
    try {
      notifier = PDPProvider.of<PDPNotifier>(context);
    } catch (e) {
      notifier = null;
    }

    final screenSize = MediaQuery.of(context).size;
    final carouselOtherLookController =
        context.locateService<CarouselOtherLookController>();
    final fullLookController = context.locateService<FullLookController>();
    final images =
        SMControllerCarrousel.getItemsOfProduct(widget.product, pdpTheme);
    final showOtherLook = pdpTheme.showOtherLookProducts &&
        (widget.product.otherLookProducts?.isNotEmpty ?? false);
    final showFullLook = config.appFeaturesConfig.fullLookConfig.isEnabled &&
        fullLookController.hasCompleteFullLook.value;

    if (showOtherLook) {
      images.add(_buildOtherLookProducts(carouselOtherLookController));
    }

    if (showFullLook) {
      images.add(_buildFullLookCarousel());
    }

    final shareEnabledCondition = pdpTheme.shareButton.enabled &&
        pdpTheme.favoriteButtonPlacement.isInformationRight == false &&
        pdpTheme.productInformationTheme.layout.isColumn == false;

    return Hero(
      tag: widget.productHeroID,
      child: Stack(
        children: [
          GestureDetector(
            onDoubleTap: () async {
              if (config.appFeaturesConfig.wishlistV2) {
                await wishlistV2OnTap();
              } else if (_showLoginFlow) {
                ModuleRegistry.root
                    .pluginsOfType<LoginPlugin>()
                    .firstOrNull
                    ?.execute(LoginFlow.pdp,
                        loginTitle: termsAndMessages.loginToAddToWishlist,
                        action: () async {
                  await addOrRemoveFromWishlist(forceFeedback: true);
                });
              } else {
                await addOrRemoveFromWishlist();
              }
            },
            child: PageView.builder(
              pageSnapping: true,
              itemCount: (showOtherLook || showFullLook) ? null : images.length,
              controller: widget.imageScrollController,
              scrollDirection: Axis.horizontal,
              onPageChanged: (index) {
                if (showOtherLook || showFullLook) {
                  ((index % images.length) + 1) == images.length
                      ? carouselOtherLookController.showNavBar.value = false
                      : carouselOtherLookController.showNavBar.value = true;
                }
              },
              itemBuilder: (BuildContext context, int index) {
                final condition =
                    (widget.product.productClusters?.containsKey("2236") ??
                            false) &&
                        pdpTheme.enableImagePadding;
                final childImage = images[(showOtherLook || showFullLook)
                        ? index % images.length
                        : index]
                    .build(context, screenSize, index);

                return InteractiveViewer(
                  maxScale: 2.2,
                  transformationController: _transformationController,
                  onInteractionEnd: (_) => _animateReset(),
                  child: condition
                      ? Column(
                          children: [
                            Container(height: 200, color: colors.brand.light1),
                            Expanded(child: childImage),
                            Container(height: 200, color: colors.brand.light1),
                          ],
                        )
                      : childImage,
                );
              },
            ),
          ),
          if (notifier != null &&
              notifier.value.activeItemIndex == 1 &&
              pdpTheme.showSportStamps)
            Builder(
              builder: (context) {
                final document = notifier!.stampCollection;
                final completeList = notifier.getProductsStampsByProduct();

                if (document == null || completeList.isEmpty) {
                  return const SizedBox.shrink();
                }

                final summaryList = notifier
                    .getProductsStampsByProduct(3)
                    .map((e) => PdpCmsStampChip(
                          url: e.icon.url,
                          title: e.title,
                        ))
                    .separated(SizedBox(height: tokens.spacingStack.xs));

                final completeWidgetList = completeList
                    .map((e) => PdpCmsStampTile(
                          url: e.icon.url,
                          title: e.title,
                          description: e.description,
                        ))
                    .separated(SizedBox(height: tokens.spacingStack.md));

                return Positioned(
                  bottom: 130,
                  left: 20,
                  child: GestureDetector(
                    onTap: () {
                      customShowBottomSheet(
                        SMBottomSheet(
                          hideButton: true,
                          title: document.stampCollectionTitle,
                          child: SingleChildScrollView(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: completeWidgetList,
                            ),
                          ),
                        ),
                      );
                    },
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: summaryList,
                    ),
                  ),
                );
              },
            ),
          if (showHeart && favoriteAnimation != null)
            Align(
              alignment: Alignment.center,
              child: SmLottie(
                  controller: _heartAnimationController,
                  name: favoriteAnimation!,
                  height: 120,
                  width: 120,
                  repeat: false,
                  onLoaded: (comp) {
                    _heartAnimationController
                      ..duration = comp.duration
                      ..forward().whenComplete(
                        () => _heartAnimationController.reset(),
                      );
                  }),
            ),
          if (pdpTheme.mainContent.isImageAndInformation)
            shareEnabledCondition
                ? Positioned(
                    bottom: 0,
                    width: screenSize.width,
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(
                              bottom: spacingStack.md, left: spacingInline.md),
                          child: SMControllerCarrousel(
                            activeItemColor: pdpTheme
                                .controllerCarouselTheme?.activeItemColor,
                            secondaryColor: pdpTheme
                                    .controllerCarouselTheme?.secondaryColor ??
                                colors.neutral.light2,
                            activeItemIndex: position ?? 0,
                            itemsAmount: images.length,
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(
                            bottom: spacingStack.sm,
                            right: spacingInline.md,
                          ),
                          child: SMButton.icon(
                            onPressed: () async {
                              Uri? uri;
                              String? finalUrl;
                              if (widget.product.productUrl != null) {
                                uri = Uri.parse(widget.product.productUrl!);
                                final storeHost = config.store.storeUrl
                                    .replaceAll(RegExp(r'http(s)?:\/\/'), '');
                                finalUrl = Uri(
                                  scheme: 'https',
                                  host: storeHost,
                                  path: uri.path,
                                ).toString();
                              }
                              await Share.share(
                                uri != null && finalUrl != null
                                    ? finalUrl
                                    : widget.product.productName!,
                              );
                            },
                            isOutlined: true,
                            backgroundColor: Colors.transparent,
                            child: Icon(icons.share, size: 40),
                          ),
                        ),
                      ],
                    ),
                  )
                : Positioned(
                    bottom: 0,
                    child: Padding(
                      padding: EdgeInsets.only(
                        bottom: spacingStack.md,
                        left: spacingInline.md,
                      ),
                      child: carouselOtherLookController.showNavBar.value
                          ? SMControllerCarrousel(
                              activeItemColor: pdpTheme
                                  .controllerCarouselTheme?.activeItemColor,
                              secondaryColor: pdpTheme.controllerCarouselTheme
                                      ?.secondaryColor ??
                                  colors.neutral.light2,
                              activeItemIndex: (showOtherLook || showFullLook)
                                  ? (position ?? 0) % images.length
                                  : (position ?? 0),
                              itemsAmount: images.length,
                            )
                          : const SizedBox.shrink(),
                    ),
                  ),
        ],
      ),
    );
  }

  bool get _showLoginFlow =>
      config.appFeaturesConfig.allowUnloggedUsersToAddWishlist == false &&
      !authController.isLoggedIn;

  bool get _isProductInWishlist => config.appFeaturesConfig.wishlistV2
      ? wishlistV2Controller.wishlist.value?.productsIds
              .contains(widget.product.productId!) ??
          false
      : _wishlist.localWishlist.value?.wishlist
              ?.contains(widget.product.productId!) ??
          false;

  Future<void> addOrRemoveFromWishlist(
      {bool showAnimation = true, bool forceFeedback = false}) async {
    try {
      if (_isProductInWishlist) {
        await _wishlist.removeProductFromWishlist(
          productId: widget.product.productId!,
          product: widget.product,
        );

        if (!config.appFeaturesConfig.wishlistV2 &&
            (mounted || forceFeedback)) {
          showSnackBar(
            SMSnackBarGetX(
              textLabel: termsAndMessages.wishlistRemovedSuccessfuly,
              textActionLabel: 'desfazer',
              onPressTextActionLabel: () {
                addOrRemoveFromWishlist(showAnimation: false);
              },
              backgroundColor:
                  wishlistTheme.snackbarTheme?.removedProductBackgroundColor,
              designTokens: tokens,
            ),
          );
        }

        logRemoveProductFromWishlist(
          productId: widget.product.productId!,
          product: widget.product,
        );
      } else {
        await _wishlist.addProductToWishlist(
            productId: widget.product.productId!, product: widget.product);
        if (showAnimation) {
          setState(() {
            showHeart = true;
          });
        }

        await logAddToWishlist(widget.product);
      }
    } catch (e, st) {
      debugPrint(e.toString());
      debugPrintStack(stackTrace: st);
    }
  }

  logAddToWishlist(Product productToWishList) {
    dispatchLogAddToWishlist(
      screenClass: 'PDPCarousel',
      context,
      productToWishList: productToWishList,
    );
  }

  logRemoveProductFromWishlist({String? productId, Product? product}) {
    dispatchRemoveFromWishlist(
      screenClass: 'PDPCarousel',
      context,
      productId: productId!,
      product: product,
    );
  }

  Future<void> addOrRemoveFromWishlistV2() async {
    if (_isProductInWishlist) {
      await wishlistV2Controller.removeProductFromAllFolders(
        product: widget.product,
      );
    } else {
      await wishlistV2Controller.addProductToSingleFolder(
        productId: widget.product.productId!,
        product: widget.product,
      );
      setState(() {
        showHeart = true;
      });
    }
    await wishlistV2Controller.getFolders();
  }

  Future<void> wishlistV2OnTap() async {
    try {
      if (_isProductInWishlist) {
        customShowBottomSheet(
          SMBottomSheetRemoveProductV2(
            onTap: () async => {await addOrRemoveFromWishlistV2()},
            onTapSecondButton: () {
              navigateBack();
              addToWishlistBottomSheet();
            },
          ),
        );
      } else {
        await addOrRemoveFromWishlistV2();
        if (config.appFeaturesConfig.wishlistV2 && mounted) {
          showSnackBar(
            SMSnackBarGetX(
              textLabel: _isProductInWishlist
                  ? termsAndMessages.wishlistSavedSuccessfuly
                  : termsAndMessages.wishlistRemovedSuccessfuly,
              textActionLabel: _isProductInWishlist ? 'salvar em pasta' : null,
              onPressTextActionLabel: () => addToWishlistBottomSheet(),
              designTokens: tokens,
            ),
          );
        }
      }
    } catch (e) {
      showSnackBar(SMSnackBarGetX(
        designTokens: tokens,
        textLabel: 'ocorreu um erro, tente novamente!',
        errorMessage: true,
        textColor: colors.typography.pure1,
      ));
    }
  }

  CarouselItem _buildOtherLookProducts(
      CarouselOtherLookController carouselOtherLookController) {
    return CarouselOtherLook(
        productIdsSeparateByComma: widget.product.otherLookProducts?.join(','),
        onTapClose: () {
          carouselOtherLookController.showNavBar.value = true;
          widget.imageScrollController.jumpToPage(0);
          setState(() {
            position = 0;
          });
        });
  }

  CarouselItem _buildFullLookCarousel() {
    return CarouselFullLook(
      onTapClose: () {
        widget.imageScrollController.jumpToPage(0);
        setState(() {
          position = 0;
        });
      },
    );
  }

  void _onScroll() {
    setState(() {
      position = widget.imageScrollController.page?.toInt();
    });
  }

  addToWishlistBottomSheet() =>
      customShowBottomSheet(SMBottomSheetAddToFolder(product: widget.product));

  void _onAnimateReset() {
    _transformationController.value = _animationReset!.value;
    if (!_controllerReset.isAnimating) {
      _animationReset!.removeListener(_onAnimateReset);
      _animationReset = null;
      _controllerReset.reset();
    }
  }

  void _animateReset() {
    _controllerReset.reset();
    _animationReset = Matrix4Tween(
      begin: _transformationController.value,
      end: Matrix4.identity(),
    ).animate(_controllerReset);
    _animationReset!.addListener(_onAnimateReset);
    _controllerReset.forward();
  }

  bool get hasAdjustmentClusters {
    final clusters = widget.product.productClusters ?? {};
    return clusters.containsKey('2236') || clusters.containsKey('2612');
  }
}

class CarouselOtherLook extends CarouselItem {
  final String? productIdsSeparateByComma;
  final void Function()? onTapClose;

  CarouselOtherLook({
    this.productIdsSeparateByComma,
    this.onTapClose,
  });

  @override
  Widget build(BuildContext context, Size screenSize, int? index) {
    final tokens = context.designTokens;
    final catalogController =
        context.serviceLocator.locate<CatalogController>();
    final appRoutes = context.locateService<AppRoutes>();
    final carouselOtherLookController =
        context.locateService<CarouselOtherLookController>();
    return LayoutBuilder(builder: (context, constraints) {
      final maxHeight = constraints.maxHeight;
      final maxWidth = constraints.maxWidth;

      return Container(
        padding: EdgeInsets.only(top: maxHeight * 0.13),
        color: const Color(0xFFE9E8E1),
        child: SMSpotProductCarousel(
          imageWidth: maxWidth * 0.55,
          imageHeight: maxHeight * 0.55,
          containerHeight: maxHeight * 0.65,
          backgroundColor: const Color(0xFFE9E8E1),
          textSecondary: tokens.textTransform
              .title(tokens.termsAndMessages.otherLookProducts),
          textSecondaryStyle: tokens.pdpTheme.sectionsTitleStyle?.copyWith(
            fontSize: tokens.typography.fontSizes.md,
          ),
          priceTextStyle: TextStyle(
            color: tokens.colors.typography.pure2,
            fontSize: tokens.typography.fontSizes.xus,
          ),
          bottomPadding: tokens.spacingStack.lg,
          onTapProduct: (Product product, int index) {
            product.updateProductHero(SMSpotProductCarousel);
            catalogController.selectedProduct(product);
            carouselOtherLookController.showNavBar.value = true;

            navigateToNamed(appRoutes.productDetails);
          },
          search: Search(
            productIdsSeparateByComma: productIdsSeparateByComma,
          ),
          screenClass: 'PDPCarousel',
          isPDPCarousel: true,
          onTapClose: onTapClose,
        ),
      );
    });
  }
}

class CarouselFullLook extends CarouselItem {
  final void Function()? onTapClose;

  CarouselFullLook({
    this.onTapClose,
  });

  @override
  Widget build(BuildContext context, Size screenSize, int? index) {
    final tokens = context.designTokens;

    return LayoutBuilder(builder: (context, constraints) {
      final maxHeight = constraints.maxHeight;
      final maxWidth = constraints.maxWidth;

      return Container(
        width: maxWidth,
        height: maxHeight,
        color: tokens.colors.brand.light1,
        child: Column(
          children: [
            // Header
            Container(
              padding: EdgeInsets.all(tokens.spacingInset.md),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Complete o Look',
                    style: tokens.typography.typeStyles.headlineMd.copyWith(
                      color: tokens.colors.typography.pure2,
                    ),
                  ),
                  if (onTapClose != null)
                    IconButton(
                      onPressed: onTapClose,
                      icon: Icon(
                        Icons.close,
                        color: tokens.colors.typography.pure2,
                      ),
                    ),
                ],
              ),
            ),
            // Conteúdo
            Expanded(
              child: Center(
                child: Text(
                  'Carrossel de produtos aqui',
                  style: tokens.typography.typeStyles.bodyLg?.copyWith(
                        color: tokens.colors.typography.pure2,
                      ) ??
                      TextStyle(
                        color: tokens.colors.typography.pure2,
                        fontSize: 16,
                      ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      );
    });
  }
}
