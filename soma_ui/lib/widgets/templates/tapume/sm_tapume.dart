import 'package:flutter/material.dart';
import 'package:soma_core/config/tapume_controller.dart';
import 'package:soma_ui/soma_ui.dart';
import 'package:soma_ui/widgets/templates/tapume/widgets/countdown_widget.dart';
import 'package:soma_ui/widgets/templates/tapume/widgets/countdown_widget_wrapping.dart';

class SmTapume extends StatelessWidget {
  const SmTapume({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final tapumeController = context.locateService<TapumeController>();
    final width = MediaQuery.of(context).size.width;
    final height = MediaQuery.of(context).size.height;
    final designTokens = context.designTokens;
    final tapume = tapumeController.tapumeEtc.isActive
        ? tapumeController.tapumeEtc
        : tapumeController.tapume;

    return Scaffold(
      body: SizedBox(
        height: height,
        width: width,
        child: GestureDetector(
          onTap: () async {
            if (tapume.isCountdown) {
              return;
            }
            final redirectUtils = context.locateService<RedirectUtils>();
            if (tapume.urlRedirect != null &&
                await redirectUtils.canLaunch(tapume.urlRedirect!)) {
              await redirectUtils.launchUri(
                Uri.parse(tapume.urlRedirect!),
              );
            }
          },
          child: tapume.image != null
              ? Stack(
                  fit: StackFit.expand,
                  children: [
                    SMCachedNetworkingImage(
                      width:
                          tapume.image!.attributes.height?.toDouble() ?? width,
                      height:
                          tapume.image!.attributes.width?.toDouble() ?? height,
                      imageUrl: tapume.image!.attributes.url,
                    ),
                    if (tapumeController.tapumeEtc.isActive == true)
                      Positioned(
                        right: 24,
                        top: 42,
                        child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              InkWell(
                                onTap: () => navigateToNamed('/'),
                                child: Icon(
                                  designTokens.icons.close,
                                  color: Colors.black,
                                  size: 32,
                                ),
                              ),
                            ]),
                      ),
                    if (tapume.isCountdown) ...[
                      Center(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            CountdownWidgetWrapping(
                              startTime: tapume.startTime ?? DateTime.now(),
                              endTime: tapume.endTime ?? DateTime.now(),
                              onFinish: () {
                                navigateToNamed('/');
                              },
                              builder: (context, remaining) {
                                return CountdownWidget(
                                  theme: tapume.countdownTheme,
                                  hours: remaining.hours,
                                  minutes: remaining.minutes,
                                  seconds: remaining.seconds,
                                );
                              },
                            ),
                            const SizedBox(height: 215),
                          ],
                        ),
                      ),
                    ],
                  ],
                )
              : const SizedBox.shrink(),
        ),
      ),
    );
  }
}
