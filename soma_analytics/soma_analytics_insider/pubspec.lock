# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: eb376e9acf6938204f90eb3b1f00b578640d3188b4c8a8ec054f9f479af8d051
      url: "https://pub.dev"
    source: hosted
    version: "64.0.0"
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: "37a42d06068e2fe3deddb2da079a8c4d105f241225ba27b7122b37e9865fd8f7"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.35"
  adyen_checkout:
    dependency: transitive
    description:
      name: adyen_checkout
      sha256: "7747c9cbd1cb4aaaa11c1da9a7f345d0510d8a7a9a1e4af12e55b32bc9465434"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: "69f54f967773f6c26c7dcb13e93d7ccee8b17a641689da39e878d5cf13b06893"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.0"
  app_settings:
    dependency: transitive
    description:
      name: app_settings
      sha256: "66715a323ac36d6c8201035ba678777c0d2ea869e4d7064300d95af10c3bb8cb"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.0"
  appsflyer_sdk:
    dependency: transitive
    description:
      name: appsflyer_sdk
      sha256: "2aa356709ab19eddfd0249e31a55a4f1f31c4e98847a36d11851d094a6a0dc88"
      url: "https://pub.dev"
    source: hosted
    version: "6.11.2"
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: "20071638cbe4e5964a427cfa0e86dce55d060bc7d82d56f3554095d7239a8765"
      url: "https://pub.dev"
    source: hosted
    version: "3.4.2"
  args:
    dependency: transitive
    description:
      name: args
      sha256: b003c3098049a51720352d219b0bb5f219b60fbfb68e7a4748139a06a5676515
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "947bfcf187f74dbc5e146c9eb9c0f10c9f8b30743e341481c1e2ed3ecc18c20c"
      url: "https://pub.dev"
    source: hosted
    version: "2.11.0"
  battery_plus:
    dependency: transitive
    description:
      name: battery_plus
      sha256: ba605aeafd6609cb5f8020c609a51941803a5fb2b6a7576f7c7eeeb52d29e750
      url: "https://pub.dev"
    source: hosted
    version: "5.0.3"
  battery_plus_platform_interface:
    dependency: transitive
    description:
      name: battery_plus_platform_interface
      sha256: "942707f90e2f7481dcb178df02e22a9c6971b3562b848d6a1b8c7cff9f1a1fec"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: "04a925763edad70e8443c99234dc3328f442e811f1d8fd1a72f1c8ad0f69a605"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: ee67cb0715911d28db6bf4af1026078bd6f0128b07a5f66fb2ed94ec6783c09a
      url: "https://pub.dev"
    source: hosted
    version: "1.18.0"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: "1be13198012c1d5bc042dc40ad1d7f16cbd522350984c0c1abf471d6d7e305c6"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  coverage:
    dependency: transitive
    description:
      name: coverage
      sha256: "17cf9a839208acaed741b1f00ac87cd1fde00548198ba57205cca45c749cb379"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: fedaadfa3a6996f75211d835aaeb8fede285dae94262485698afd832371b9a5e
      url: "https://pub.dev"
    source: hosted
    version: "0.3.3+8"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: aa274aa7774f8964e4f4f38cc994db7b6158dd36e9187aaceaddc994b35c6c67
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "831883fb353c8bdc1d71979e5b342c7d88acfbc643113c14ae51e2442ea0f20f"
      url: "https://pub.dev"
    source: hosted
    version: "0.17.3"
  cupertino_icons:
    dependency: transitive
    description:
      name: cupertino_icons
      sha256: ba631d1c7f7bef6b729a622b7b752645a2d076dba9976925b8f25725a30e1ee6
      url: "https://pub.dev"
    source: hosted
    version: "1.0.8"
  date_picker_plus:
    dependency: transitive
    description:
      name: date_picker_plus
      sha256: fda3fc7a2da8a9af1b7cf862c529143c98ceadb5d49870e2b28612c3f0d89422
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "365c771ac3b0e58845f39ec6deebc76e3276aa9922b0cc60840712094d9047ac"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.10"
  device_info_plus:
    dependency: transitive
    description:
      name: device_info_plus
      sha256: "77f757b789ff68e4eaf9c56d1752309bd9f7ad557cb105b938a7f8eb89e59110"
      url: "https://pub.dev"
    source: hosted
    version: "9.1.2"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      sha256: d3b01d5868b50ae571cd1dc6e502fc94d956b665756180f7b16ead09e836fd64
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  diacritic:
    dependency: transitive
    description:
      name: diacritic
      sha256: c09a420e737dc036122121fea9f774767e95068f34a17894ee9db30c5bda3075
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3"
  dio:
    dependency: transitive
    description:
      name: dio
      sha256: "253a18bbd4851fecba42f7343a1df3a9a4c1d31a2c1b37e221086b4fa8c8dbc9"
      url: "https://pub.dev"
    source: hosted
    version: "5.8.0+1"
  dio_cache_interceptor:
    dependency: transitive
    description:
      name: dio_cache_interceptor
      sha256: "1346705a2057c265014d7696e3e2318b560bfb00b484dac7f9b01e2ceaebb07d"
      url: "https://pub.dev"
    source: hosted
    version: "3.5.1"
  dio_web_adapter:
    dependency: transitive
    description:
      name: dio_web_adapter
      sha256: "0a2e95fc6bdeb623bb623fc41e90e6924e9a3bbd65089f9221f83c185366b479"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  expandable_page_view:
    dependency: transitive
    description:
      name: expandable_page_view
      sha256: "210dc6961cfc29f7ed42867824eb699c9a4b9b198a7c04b8bdc1c05844969dc6"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.17"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: a38574032c5f1dd06c4aee541789906c12ccaab8ba01446e800d9c5b79c4a978
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  file:
    dependency: transitive
    description:
      name: file
      sha256: "1b92bec4fc2a72f59a8e15af5f52cd441e4a7860b49499d69dfa817af20e925d"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.4"
  file_utils:
    dependency: transitive
    description:
      name: file_utils
      sha256: d1e64389a22649095c8405c9e177272caf05139255931c9ff30d53b5c9bcaa34
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  firebase_analytics:
    dependency: transitive
    description:
      name: firebase_analytics
      sha256: dbf1e7ab22cfb1f4a4adb103b46a26276b4edc593d4a78ef6fb942bafc92e035
      url: "https://pub.dev"
    source: hosted
    version: "10.10.7"
  firebase_analytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_analytics_platform_interface
      sha256: "3729b74f8cf1d974a27ba70332ecb55ff5ff560edc8164a6469f4a055b429c37"
      url: "https://pub.dev"
    source: hosted
    version: "3.10.8"
  firebase_analytics_web:
    dependency: transitive
    description:
      name: firebase_analytics_web
      sha256: "019cd7eee74254d33fbd2e29229367ce33063516bf6b3258a341d89e3b0f1655"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.7+7"
  firebase_core:
    dependency: transitive
    description:
      name: firebase_core
      sha256: "26de145bb9688a90962faec6f838247377b0b0d32cc0abecd9a4e43525fc856c"
      url: "https://pub.dev"
    source: hosted
    version: "2.32.0"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      sha256: "8bcfad6d7033f5ea951d15b867622a824b13812178bfec0c779b9d81de011bbb"
      url: "https://pub.dev"
    source: hosted
    version: "5.4.2"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: "362e52457ed2b7b180964769c1e04d1e0ea0259fdf7025fdfedd019d4ae2bd88"
      url: "https://pub.dev"
    source: hosted
    version: "2.17.5"
  firebase_crashlytics:
    dependency: transitive
    description:
      name: firebase_crashlytics
      sha256: "9897c01efaa950d2f6da8317d12452749a74dc45f33b46390a14cfe28067f271"
      url: "https://pub.dev"
    source: hosted
    version: "3.5.7"
  firebase_crashlytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_crashlytics_platform_interface
      sha256: "16a71e08fbf6e00382816e1b13397898c29a54fa0ad969c2c2a3b82a704877f0"
      url: "https://pub.dev"
    source: hosted
    version: "3.6.35"
  firebase_performance:
    dependency: transitive
    description:
      name: firebase_performance
      sha256: dbcfc300755c4bb866988de20a491f0b53e1a0d14c375a2c31aa53ca82174c5b
      url: "https://pub.dev"
    source: hosted
    version: "0.9.4+7"
  firebase_performance_dio:
    dependency: transitive
    description:
      name: firebase_performance_dio
      sha256: aeec00853f9538feeac11edeceaefdd7115b099df9beee9b2fe53dc1236fdb33
      url: "https://pub.dev"
    source: hosted
    version: "0.7.1"
  firebase_performance_platform_interface:
    dependency: transitive
    description:
      name: firebase_performance_platform_interface
      sha256: "191c9945c2ea4359cb57dc086463b2a25b0f9d8d42f66a0be4c1a7133e26ebc8"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.4+35"
  firebase_performance_web:
    dependency: transitive
    description:
      name: firebase_performance_web
      sha256: "9f03a53f55697b206393366bf138e382cbd845d5021b5be6f7fc97b338da2cb5"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.6+7"
  firebase_remote_config:
    dependency: transitive
    description:
      name: firebase_remote_config
      sha256: "653bd94b68e2c4e89eca10db90576101f1024151f39f2d4e7c64ae6a90a5f9c5"
      url: "https://pub.dev"
    source: hosted
    version: "4.4.7"
  firebase_remote_config_platform_interface:
    dependency: transitive
    description:
      name: firebase_remote_config_platform_interface
      sha256: "24a2c445b15de3af7e4582ebceb2aa9a1e3731d0202cb3e7a1e03012440fa07d"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.35"
  firebase_remote_config_web:
    dependency: transitive
    description:
      name: firebase_remote_config_web
      sha256: "525aa3000fd27cd023841c802010a06515e564aab2f147aa964b35f54abbf449"
      url: "https://pub.dev"
    source: hosted
    version: "1.6.7"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: b6dc7065e46c974bc7c5f143080a6764ec7a4be6da1285ececdc37be96de53be
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_html:
    dependency: transitive
    description:
      name: flutter_html
      sha256: "02ad69e813ecfc0728a455e4bf892b9379983e050722b1dce00192ee2e41d1ee"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0-beta.2"
  flutter_inappwebview:
    dependency: transitive
    description:
      path: "."
      ref: HEAD
      resolved-ref: "3946536e294f3cd3afd022a6df41b128593d6019"
      url: "https://diegoassis1:<EMAIL>/somalabs/flutter_inappwebview_fork.git"
    source: git
    version: "5.8.0"
  flutter_insider:
    dependency: "direct main"
    description:
      name: flutter_insider
      sha256: "96103d6f52911cca924406412d89ecf5cc8c9b853ffea870a7e81bc39d983e1a"
      url: "https://pub.dev"
    source: hosted
    version: "3.18.1+nh"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: aeb0b80a8b3709709c9cc496cdc027c5b3216796bc0af0ce1007eaf24464fd4c
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  flutter_localizations:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_secure_storage:
    dependency: transitive
    description:
      name: flutter_secure_storage
      sha256: "22dbf16f23a4bcf9d35e51be1c84ad5bb6f627750565edd70dab70f3ff5fff8f"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.0"
  flutter_secure_storage_linux:
    dependency: transitive
    description:
      name: flutter_secure_storage_linux
      sha256: "3d5032e314774ee0e1a7d0a9f5e2793486f0dff2dd9ef5a23f4e3fb2a0ae6a9e"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  flutter_secure_storage_macos:
    dependency: transitive
    description:
      name: flutter_secure_storage_macos
      sha256: bd33935b4b628abd0b86c8ca20655c5b36275c3a3f5194769a7b3f37c905369c
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  flutter_secure_storage_platform_interface:
    dependency: transitive
    description:
      name: flutter_secure_storage_platform_interface
      sha256: "0d4d3a5dd4db28c96ae414d7ba3b8422fd735a8255642774803b2532c9a61d7e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  flutter_secure_storage_web:
    dependency: transitive
    description:
      name: flutter_secure_storage_web
      sha256: "30f84f102df9dcdaa2241866a958c2ec976902ebdaa8883fbfe525f1f2f3cf20"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  flutter_secure_storage_windows:
    dependency: transitive
    description:
      name: flutter_secure_storage_windows
      sha256: "38f9501c7cb6f38961ef0e1eacacee2b2d4715c63cc83fe56449c4d3d0b47255"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  flutter_slidable:
    dependency: transitive
    description:
      name: flutter_slidable
      sha256: "19ed4813003a6ff4e9c6bcce37e792a2a358919d7603b2b31ff200229191e44c"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  flutter_svg:
    dependency: transitive
    description:
      name: flutter_svg
      sha256: f991fdb1533c3caeee0cdc14b04f50f0c3916f0dbcbc05237ccbe4e3c6b93f3f
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      sha256: "408e3ca148b31c20282ad6f37ebfa6f4bdc8fede5b74bc2f08d9d92b55db3612"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  geocoding:
    dependency: transitive
    description:
      name: geocoding
      sha256: d580c801cba9386b4fac5047c4c785a4e19554f46be42f4f5e5b7deacd088a66
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  geocoding_android:
    dependency: transitive
    description:
      name: geocoding_android
      sha256: "1b13eca79b11c497c434678fed109c2be020b158cec7512c848c102bc7232603"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  geocoding_ios:
    dependency: transitive
    description:
      name: geocoding_ios
      sha256: "94ddba60387501bd1c11e18dca7c5a9e8c645d6e3da9c38b9762434941870c24"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  geocoding_platform_interface:
    dependency: transitive
    description:
      name: geocoding_platform_interface
      sha256: "8c2c8226e5c276594c2e18bfe88b19110ed770aeb7c1ab50ede570be8b92229b"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  geolocator:
    dependency: transitive
    description:
      name: geolocator
      sha256: "5c23f3613f50586c0bbb2b8f970240ae66b3bd992088cf60dd5ee2e6f7dde3a8"
      url: "https://pub.dev"
    source: hosted
    version: "9.0.2"
  geolocator_android:
    dependency: transitive
    description:
      name: geolocator_android
      sha256: "2ba24690aee0a3e1b6b7bd47c2711a50c874e95e4c758346589d35194adf6d6a"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.7"
  geolocator_apple:
    dependency: transitive
    description:
      name: geolocator_apple
      sha256: "22b60ca3b8c0f58e6a9688ff855ee39ab813ca3f0c0609a48d282f6631266f2e"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.5"
  geolocator_platform_interface:
    dependency: transitive
    description:
      name: geolocator_platform_interface
      sha256: af4d69231452f9620718588f41acc4cb58312368716bfff2e92e770b46ce6386
      url: "https://pub.dev"
    source: hosted
    version: "4.0.7"
  geolocator_web:
    dependency: transitive
    description:
      name: geolocator_web
      sha256: f68a122da48fcfff68bbc9846bb0b74ef651afe84a1b1f6ec20939de4d6860e1
      url: "https://pub.dev"
    source: hosted
    version: "2.1.6"
  geolocator_windows:
    dependency: transitive
    description:
      name: geolocator_windows
      sha256: f5911c88e23f48b598dd506c7c19eff0e001645bdc03bb6fecb9f4549208354d
      url: "https://pub.dev"
    source: hosted
    version: "0.1.1"
  get:
    dependency: transitive
    description:
      name: get
      sha256: "2ba20a47c8f1f233bed775ba2dd0d3ac97b4cf32fc17731b3dfc672b06b0e92a"
      url: "https://pub.dev"
    source: hosted
    version: "4.6.5"
  get_storage:
    dependency: transitive
    description:
      name: get_storage
      sha256: "39db1fffe779d0c22b3a744376e86febe4ade43bf65e06eab5af707dc84185a2"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: "4515b5b6ddb505ebdd242a5f2cc5d22d3d6a80013789debfbda7777f47ea308c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  globbing:
    dependency: transitive
    description:
      name: globbing
      sha256: "4f89cfaf6fa74c9c1740a96259da06bd45411ede56744e28017cc534a12b6e2d"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  html:
    dependency: transitive
    description:
      name: html
      sha256: "3a7812d5bcd2894edf53dfaf8cd640876cf6cef50a8f238745c8b8120ea74d3a"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.4"
  http:
    dependency: transitive
    description:
      name: http
      sha256: "6aa2946395183537c8b880962d935877325d6a09a2867c3970c05c0fed6ac482"
      url: "https://pub.dev"
    source: hosted
    version: "0.13.5"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      sha256: "97486f20f9c2f7be8f514851703d0119c3596d14ea63227af6f7a481ef2b2f8b"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  in_app_review:
    dependency: transitive
    description:
      name: in_app_review
      sha256: "41ec6f30427ab09eb6ae1c85c4a2a624a145fc5d726f023de4d97170ec9e5466"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.8"
  in_app_review_platform_interface:
    dependency: transitive
    description:
      name: in_app_review_platform_interface
      sha256: fed2c755f2125caa9ae10495a3c163aa7fab5af3585a9c62ef4a6920c5b45f10
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  intl:
    dependency: transitive
    description:
      name: intl
      sha256: "3bc132a9dbce73a7e4a21a17d06e1878839ffbf975568bc875c60537824b0c4d"
      url: "https://pub.dev"
    source: hosted
    version: "0.18.1"
  io:
    dependency: transitive
    description:
      name: io
      sha256: "2ec25704aba361659e10e3e5f5d672068d332fc8ac516421d483a11e5cbd061e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  js:
    dependency: transitive
    description:
      name: js
      sha256: f2c445dce49627136094980615a031419f7f3eb393237e4ecd97ac15dea343f3
      url: "https://pub.dev"
    source: hosted
    version: "0.6.7"
  jwt_decoder:
    dependency: transitive
    description:
      name: jwt_decoder
      sha256: "54774aebf83f2923b99e6416b4ea915d47af3bde56884eb622de85feabbc559f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: "5e4a9cd06d447758280a8ac2405101e0e2094d2a1dbdd3756aec3fe7775ba593"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  list_counter:
    dependency: transitive
    description:
      name: list_counter
      sha256: c447ae3dfcd1c55f0152867090e67e219d42fe6d4f2807db4bbe8b8d69912237
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: c0bbfe94d46aedf9b8b3e695cf3bd48c8e14b35e3b2c639e0aa7755d589ba946
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  lottie:
    dependency: transitive
    description:
      name: lottie
      sha256: a93542cc2d60a7057255405f62252533f8e8956e7e06754955669fd32fb4b216
      url: "https://pub.dev"
    source: hosted
    version: "2.7.0"
  mask_text_input_formatter:
    dependency: transitive
    description:
      name: mask_text_input_formatter
      sha256: "978c58ec721c25621ceb468e633f4eef64b64d45424ac4540e0565d4f7c800cd"
      url: "https://pub.dev"
    source: hosted
    version: "2.9.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: "1803e76e6653768d64ed8ff2e1e67bea3ad4b923eb5c56a295c3e634bad5960e"
      url: "https://pub.dev"
    source: hosted
    version: "0.12.16"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: "9528f2f296073ff54cb9fee677df673ace1218163c3bc7628093e7eed5203d41"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: a6e590c838b18133bb482a2745ad77c5bb7715fb0451209e1a7567d416678b8e
      url: "https://pub.dev"
    source: hosted
    version: "1.10.0"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: "801fd0b26f14a4a58ccb09d5892c3fbdeff209594300a542492cf13fba9d247a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  mocktail:
    dependency: transitive
    description:
      name: mocktail
      sha256: "80a996cd9a69284b3dc521ce185ffe9150cde69767c2d3a0720147d93c0cef53"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0"
  node_preamble:
    dependency: transitive
    description:
      name: node_preamble
      sha256: "6e7eac89047ab8a8d26cf16127b5ed26de65209847630400f9aefd7cd5c730db"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: "1c5b77ccc91e4823a5af61ee74e6b972db1ef98c2ff5a18d3161c982a55448bd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  package_info_plus:
    dependency: transitive
    description:
      name: package_info_plus
      sha256: "7e76fad405b3e4016cd39d08f455a4eb5199723cf594cd1b8916d47140d93017"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: "9bc8ba46813a4cc42c66ab781470711781940780fd8beddd0c3da62506d3a6c6"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "8829d8a55c13fc0e37127c29fedf290c102f4e40ae94ada574091fe0ff96c917"
      url: "https://pub.dev"
    source: hosted
    version: "1.8.3"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: e3e67b1629e6f7e8100b367d3db6ba6af4b1f0bb80f64db18ef1fbabd2fa9ccf
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_provider:
    dependency: transitive
    description:
      name: path_provider
      sha256: c7edf82217d4b2952b2129a61d3ad60f1075b9299e629e149a8d2e39c2e6aad4
      url: "https://pub.dev"
    source: hosted
    version: "2.0.14"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: "019f18c9c10ae370b08dce1f3e3b73bc9f58e7f087bb5e921f06529438ac0ae7"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.24"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "818b2dc38b0f178e0ea3f7cf3b28146faab11375985d815942a68eee11c2d0f7"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: "2ae08f2216225427e64ad224a24354221c2c7907e448e6e0e8b57b1eb9f10ad1"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.10"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "57585299a729335f1298b43245842678cb9f43a6310351b18fb577d6e33165ec"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.6"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: "1cb68ba4cd3a795033de62ba1b7b4564dace301f952de6bfb3cd91b202b6ee96"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.7"
  pay:
    dependency: transitive
    description:
      name: pay
      sha256: ad904db0e06848cade6990a3ce1e10e921ae48f7ee06447873e07b9688ac1fc5
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  pay_android:
    dependency: transitive
    description:
      name: pay_android
      sha256: aa46cd0ece1807d3fa293113fdb84afb5fc4b6ed60cf09a4886b753acb300859
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  pay_ios:
    dependency: transitive
    description:
      name: pay_ios
      sha256: "75ccb285d03f22b136c58ab8e8e0c4b614ee52a8b67e6ccfb680d4d8c04a70f6"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.11"
  pay_platform_interface:
    dependency: transitive
    description:
      name: pay_platform_interface
      sha256: "26a379e33c46508987c7afee8cde6f4aca5b5ab0afc697c27efbd33a9c2ea82a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  permission_handler:
    dependency: transitive
    description:
      name: permission_handler
      sha256: "63e5216aae014a72fe9579ccd027323395ce7a98271d9defa9d57320d001af81"
      url: "https://pub.dev"
    source: hosted
    version: "10.4.3"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      sha256: d74e77a5ecd38649905db0a7d05ef16bed42ff263b9efb73ed794317c5764ec3
      url: "https://pub.dev"
    source: hosted
    version: "10.3.4"
  permission_handler_apple:
    dependency: transitive
    description:
      name: permission_handler_apple
      sha256: "99e220bce3f8877c78e4ace901082fb29fa1b4ebde529ad0932d8d664b34f3f5"
      url: "https://pub.dev"
    source: hosted
    version: "9.1.4"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: "7c6b1500385dd1d2ca61bb89e2488ca178e274a69144d26bbd65e33eae7c02a9"
      url: "https://pub.dev"
    source: hosted
    version: "3.11.3"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      sha256: cc074aace208760f1eee6aa4fae766b45d947df85bc831cde77009cdb4720098
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: c15605cd28af66339f8eb6fbe0e541bfe2d1b72d5825efc6598f3e0a31b9ad27
      url: "https://pub.dev"
    source: hosted
    version: "6.0.2"
  pinch_zoom:
    dependency: transitive
    description:
      name: pinch_zoom
      sha256: ad12872281742726afaf03438d99a4572c584a612630768953beb6dfd6f9389a
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  pinput:
    dependency: transitive
    description:
      name: pinput
      sha256: "543da5bfdefd9e06914a12100f8c9156f84cef3efc14bca507c49e966c5b813b"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "4a451831508d7d6ca779f7ac6e212b4023dd5a7d08a27a63da33756410e32b76"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      sha256: "43ac87de6e10afabc85c445745a7b799e04de84cebaa4fd7bf55a5e1e9604d29"
      url: "https://pub.dev"
    source: hosted
    version: "3.7.4"
  pool:
    dependency: transitive
    description:
      name: pool
      sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  pretty_dio_logger:
    dependency: transitive
    description:
      name: pretty_dio_logger
      sha256: "36f2101299786d567869493e2f5731de61ce130faa14679473b26905a92b6407"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  process:
    dependency: transitive
    description:
      name: process
      sha256: "53fd8db9cec1d37b0574e12f07520d582019cb6c44abf5479a01505099a34a09"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.4"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "307de764d305289ff24ad257ad5c5793ce56d04947599ad68b3baa124105fc17"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  qr:
    dependency: transitive
    description:
      name: qr
      sha256: "64957a3930367bf97cc211a5af99551d630f2f4625e38af10edd6b19131b64b3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  qr_flutter:
    dependency: transitive
    description:
      name: qr_flutter
      sha256: "5095f0fc6e3f71d08adef8feccc8cea4f12eec18a2e31c2e8d82cb6019f4b097"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  recaptcha_enterprise_flutter:
    dependency: transitive
    description:
      name: recaptcha_enterprise_flutter
      sha256: "187e2b45734cefa5567be549e333dc154049c545a85746db0158aca347cd5ebd"
      url: "https://pub.dev"
    source: hosted
    version: "18.4.2"
  share_plus:
    dependency: transitive
    description:
      name: share_plus
      sha256: "3ef39599b00059db0990ca2e30fca0a29d8b37aae924d60063f8e0184cf20900"
      url: "https://pub.dev"
    source: hosted
    version: "7.2.2"
  share_plus_platform_interface:
    dependency: transitive
    description:
      name: share_plus_platform_interface
      sha256: "251eb156a8b5fa9ce033747d73535bf53911071f8d3b6f4f0b578505ce0d4496"
      url: "https://pub.dev"
    source: hosted
    version: "3.4.0"
  shelf:
    dependency: transitive
    description:
      name: shelf
      sha256: c24a96135a2ccd62c64b69315a14adc5c3419df63b4d7c05832a346fdb73682c
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  shelf_packages_handler:
    dependency: transitive
    description:
      name: shelf_packages_handler
      sha256: aef74dc9195746a384843102142ab65b6a4735bb3beea791e63527b88cc83306
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  shelf_static:
    dependency: transitive
    description:
      name: shelf_static
      sha256: e792b76b96a36d4a41b819da593aff4bdd413576b3ba6150df5d8d9996d2e74c
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      sha256: a988c0e8d8ffbdb8a28aa7ec8e449c260f3deb808781fe1284d22c5bba7156e8
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  showcaseview:
    dependency: transitive
    description:
      name: showcaseview
      sha256: dc62ce38820dead4a27ce39d9e6c98384be89c2f2b4da3255238a59b041c7ccd
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  smart_auth:
    dependency: transitive
    description:
      name: smart_auth
      sha256: a25229b38c02f733d0a4e98d941b42bed91a976cb589e934895e60ccfa674cf6
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  soma_analytics:
    dependency: "direct dev"
    description:
      path: ".."
      relative: true
    source: path
    version: "0.0.1"
  soma_app_omnilogic_module:
    dependency: transitive
    description:
      path: "../../soma_app_commons/modules/omnilogic"
      relative: true
    source: path
    version: "0.0.1"
  soma_core:
    dependency: "direct dev"
    description:
      path: "../../soma_core"
      relative: true
    source: path
    version: "0.0.1"
  soma_ui:
    dependency: "direct dev"
    description:
      path: "../../soma_ui"
      relative: true
    source: path
    version: "0.1.0"
  source_map_stack_trace:
    dependency: transitive
    description:
      name: source_map_stack_trace
      sha256: "84cf769ad83aa6bb61e0aa5a18e53aea683395f196a6f39c4c881fb90ed4f7ae"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  source_maps:
    dependency: transitive
    description:
      name: source_maps
      sha256: "52de2200bb098de739794c82d09c41ac27b2e42fd7e23cce7b9c74bf653c7296"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.10"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "53e943d4206a5e30df338fd4c6e7a077e02254531b138a15aec3bd143c1a8b3c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.0"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "73713990125a6d93122541237550ee3352a2d84baad52d375a4cad2eb9b7ce0b"
      url: "https://pub.dev"
    source: hosted
    version: "1.11.1"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: ba2aa5d8cc609d96bbb2899c28934f9e1af5cddbd60a827822ea467161eb54e7
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: ad47125e588cfd37a9a7f86c7d6356dde8dfe89d071d293f80ca9e9273a33871
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  super_tooltip:
    dependency: transitive
    description:
      name: super_tooltip
      sha256: "06f734caa2ddb2313813dac2c939f096222beece9c146ec7d71e07e6e869bef2"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  test:
    dependency: transitive
    description:
      name: test
      sha256: a1f7595805820fcc05e5c52e3a231aedd0b72972cb333e8c738a8b1239448b6f
      url: "https://pub.dev"
    source: hosted
    version: "1.24.9"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: "5c2f730018264d276c20e4f1503fd1308dfbbae39ec8ee63c5236311ac06954b"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.1"
  test_core:
    dependency: transitive
    description:
      name: test_core
      sha256: a757b14fc47507060a162cc2530d9a4a2f92f5100a952c7443b5cad5ef5b106a
      url: "https://pub.dev"
    source: hosted
    version: "0.5.9"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: "26f87ade979c47a150c9eaab93ccd2bebe70a27dc0b4b29517f2904f04eb11a5"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  universal_platform:
    dependency: transitive
    description:
      name: universal_platform
      sha256: d315be0f6641898b280ffa34e2ddb14f3d12b1a37882557869646e0cc363d0cc
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0+1"
  upower:
    dependency: transitive
    description:
      name: upower
      sha256: cf042403154751180affa1d15614db7fa50234bc2373cd21c3db666c38543ebf
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  url_launcher:
    dependency: transitive
    description:
      name: url_launcher
      sha256: "21b704ce5fa560ea9f3b525b43601c678728ba46725bab9b01187b4831377ed3"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "17cd5e205ea615e2c6ea7a77323a11712dffa0720a8a90540db57a01347f9ad9"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.2"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: e43b677296fadce447e987a2f519dcf5f6d1e527dc35d01ffab4fff5b8a7063e
      url: "https://pub.dev"
    source: hosted
    version: "6.3.1"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: ab360eb661f8879369acac07b6bb3ff09d9471155357da8443fd5d3cf7363811
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: "9a1a42d5d2d95400c795b2914c36fdcb525870c752569438e4ebb09a2b5d90de"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "552f8a1e663569be95a8190206a38187b531910283c3e982193e4f2733f01029"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: fff0932192afeedf63cdd50ecbb1bc825d31aed259f02bb8dba0f3b729a5e88b
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "49c10f879746271804767cb45551ec5592cdab00ee105c06dddde1a98f73b185"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: a5be9ef6618a7ac1e964353ef476418026db906c4facdedaa299b7a2e71690ff
      url: "https://pub.dev"
    source: hosted
    version: "4.5.1"
  vector_graphics:
    dependency: transitive
    description:
      name: vector_graphics
      sha256: "4ac59808bbfca6da38c99f415ff2d3a5d7ca0a6b4809c71d9cf30fba5daf9752"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.10+1"
  vector_graphics_codec:
    dependency: transitive
    description:
      name: vector_graphics_codec
      sha256: f3247e7ab0ec77dc759263e68394990edc608fb2b480b80db8aa86ed09279e33
      url: "https://pub.dev"
    source: hosted
    version: "1.1.10+1"
  vector_graphics_compiler:
    dependency: transitive
    description:
      name: vector_graphics_compiler
      sha256: "18489bdd8850de3dd7ca8a34e0c446f719ec63e2bab2e7a8cc66a9028dd76c5a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.10+1"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  video_player:
    dependency: transitive
    description:
      name: video_player
      sha256: fbf28ce8bcfe709ad91b5789166c832cb7a684d14f571a81891858fefb5bb1c2
      url: "https://pub.dev"
    source: hosted
    version: "2.8.2"
  video_player_android:
    dependency: transitive
    description:
      name: video_player_android
      sha256: "4dd9b8b86d70d65eecf3dcabfcdfbb9c9115d244d022654aba49a00336d540c2"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.12"
  video_player_avfoundation:
    dependency: transitive
    description:
      name: video_player_avfoundation
      sha256: "309e3962795e761be010869bae65c0b0e45b5230c5cee1bec72197ca7db040ed"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.6"
  video_player_platform_interface:
    dependency: transitive
    description:
      name: video_player_platform_interface
      sha256: be72301bf2c0150ab35a8c34d66e5a99de525f6de1e8d27c0672b836fe48f73a
      url: "https://pub.dev"
    source: hosted
    version: "6.2.1"
  video_player_web:
    dependency: transitive
    description:
      name: video_player_web
      sha256: "34beb3a07d4331a24f7e7b2f75b8e2b103289038e07e65529699a671b6a6e2cb"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  visibility_detector:
    dependency: transitive
    description:
      name: visibility_detector
      sha256: dd5cc11e13494f432d15939c3aa8ae76844c42b723398643ce9addb88a5ed420
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0+2"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: e686ae49284939abc06972e25f634ccdb5007d5664c4dfa1995002e8b6aa27a9
      url: "https://pub.dev"
    source: hosted
    version: "8.3.0"
  wakelock_plus:
    dependency: transitive
    description:
      name: wakelock_plus
      sha256: f268ca2116db22e57577fb99d52515a24bdc1d570f12ac18bb762361d43b043d
      url: "https://pub.dev"
    source: hosted
    version: "1.1.4"
  wakelock_plus_platform_interface:
    dependency: transitive
    description:
      name: wakelock_plus_platform_interface
      sha256: "40fabed5da06caff0796dc638e1f07ee395fb18801fbff3255a2372db2d80385"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: "6a7f46926b01ce81bfc339da6a7f20afbe7733eff9846f6d6a5466aa4c6667c0"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  web:
    dependency: "direct overridden"
    description:
      name: web
      sha256: afe077240a270dcfd2aafe77602b4113645af95d0ad31128cc02bce5ac5d5152
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: d88238e5eac9a42bb43ca4e721edba3c08c6354d4a53063afaa568516217621b
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  webkit_inspection_protocol:
    dependency: transitive
    description:
      name: webkit_inspection_protocol
      sha256: "67d3a8b6c79e1987d19d848b0892e582dbb0c66c57cc1fef58a177dd2aa2823d"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  webview_flutter:
    dependency: transitive
    description:
      name: webview_flutter
      sha256: "6869c8786d179f929144b4a1f86e09ac0eddfe475984951ea6c634774c16b522"
      url: "https://pub.dev"
    source: hosted
    version: "4.8.0"
  webview_flutter_android:
    dependency: transitive
    description:
      name: webview_flutter_android
      sha256: "0d21cfc3bfdd2e30ab2ebeced66512b91134b39e72e97b43db2d47dda1c4e53a"
      url: "https://pub.dev"
    source: hosted
    version: "3.16.3"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      sha256: d937581d6e558908d7ae3dc1989c4f87b786891ab47bb9df7de548a151779d8d
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  webview_flutter_wkwebview:
    dependency: transitive
    description:
      name: webview_flutter_wkwebview
      sha256: "9c62cc46fa4f2d41e10ab81014c1de470a6c6f26051a2de32111b2ee55287feb"
      url: "https://pub.dev"
    source: hosted
    version: "3.14.0"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: "7dacfda1edcca378031db9905ad7d7bd56b29fd1a90b0908b71a52a12c41e36b"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.3"
  win32_registry:
    dependency: transitive
    description:
      name: win32_registry
      sha256: e4506d60b7244251bc59df15656a3093501c37fb5af02105a944d73eb95be4c9
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: ee1505df1426458f7f60aac270645098d318a8b4766d85fde75f76f2e21807d1
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: b015a8ad1c488f66851d762d3090a21c600e479dc75e68328c52774040cf9226
      url: "https://pub.dev"
    source: hosted
    version: "6.5.0"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: "23812a9b125b48d4007117254bca50abb6c712352927eece9e155207b1db2370"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
sdks:
  dart: ">=3.2.3 <4.0.0"
  flutter: ">=3.16.6"
